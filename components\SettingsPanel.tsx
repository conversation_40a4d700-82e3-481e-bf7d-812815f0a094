import React, { useState, useEffect } from 'react';
import { getUserPreferences, UserPreferences } from '../services/userPreferencesService.ts';
import { CogIcon, MoonIcon, SunIcon, ComputerDesktopIcon, MagnifyingGlassIcon } from './icons.tsx';

interface SettingsPanelProps {
  onClose: () => void;
}

export const SettingsPanel: React.FC<SettingsPanelProps> = ({ onClose }) => {
  const [preferences, setPreferences] = useState<UserPreferences | null>(null);
  const [activeTab, setActiveTab] = useState<'general' | 'display' | 'search' | 'privacy'>(
    'general'
  );

  useEffect(() => {
    const userPrefs = getUserPreferences();
    setPreferences(userPrefs.getPreferences());
  }, []);

  const updatePreference = <K extends keyof UserPreferences>(key: K, value: UserPreferences[K]) => {
    if (!preferences) return;

    const userPrefs = getUserPreferences();
    userPrefs.updatePreferences({ [key]: value });
    setPreferences(userPrefs.getPreferences());
  };

  const handleExportPreferences = () => {
    const userPrefs = getUserPreferences();
    const exported = userPrefs.exportPreferences();

    const blob = new Blob([exported], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `electronics_db_preferences_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleImportPreferences = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = e => {
      try {
        const content = e.target?.result as string;
        const userPrefs = getUserPreferences();

        if (userPrefs.importPreferences(content)) {
          setPreferences(userPrefs.getPreferences());
          alert('Preferences imported successfully!');
        } else {
          alert('Failed to import preferences. Please check the file format.');
        }
      } catch (error) {
        alert('Failed to import preferences. Invalid file format.');
      }
    };
    reader.readAsText(file);
    event.target.value = '';
  };

  const handleResetPreferences = () => {
    if (confirm('Are you sure you want to reset all preferences to defaults?')) {
      const userPrefs = getUserPreferences();
      userPrefs.resetToDefaults();
      setPreferences(userPrefs.getPreferences());
    }
  };

  if (!preferences) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-slate-800 p-6 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700">
          <div className="flex items-center gap-3">
            <CogIcon className="w-6 h-6 text-primary-500" />
            <h2 className="text-xl font-bold text-slate-800 dark:text-slate-100">Settings</h2>
          </div>
          <button
            onClick={onClose}
            className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
          >
            ✕
          </button>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Sidebar */}
          <div className="w-64 border-r border-slate-200 dark:border-slate-700 p-4">
            <nav className="space-y-2">
              {[
                { id: 'general', label: 'General', icon: CogIcon },
                { id: 'display', label: 'Display', icon: ComputerDesktopIcon },
                { id: 'search', label: 'Search', icon: MagnifyingGlassIcon },
                { id: 'privacy', label: 'Privacy', icon: SunIcon },
              ].map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                    activeTab === tab.id
                      ? 'bg-primary-100 text-primary-700 dark:bg-primary-900/20 dark:text-primary-300'
                      : 'text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {/* General Tab */}
            {activeTab === 'general' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
                  General Settings
                </h3>

                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Default Mode
                  </label>
                  <select
                    value={preferences.defaultMode}
                    onChange={e => updatePreference('defaultMode', e.target.value as any)}
                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
                  >
                    <option value="general">General Items</option>
                    <option value="computer">Computer Entry</option>
                    <option value="lookup">Computer Lookup</option>
                    <option value="component">Component Search</option>
                    <option value="database">Database Browser</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Items Per Page
                  </label>
                  <select
                    value={preferences.itemsPerPage}
                    onChange={e => updatePreference('itemsPerPage', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
                  >
                    <option value={10}>10</option>
                    <option value={20}>20</option>
                    <option value={50}>50</option>
                    <option value={100}>100</option>
                  </select>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Auto Backup
                    </label>
                    <p className="text-xs text-slate-500 dark:text-slate-400">
                      Automatically backup your database
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={preferences.autoBackup}
                    onChange={e => updatePreference('autoBackup', e.target.checked)}
                    className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
                  />
                </div>
              </div>
            )}

            {/* Display Tab */}
            {activeTab === 'display' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
                  Display Settings
                </h3>

                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
                    Theme
                  </label>
                  <div className="grid grid-cols-3 gap-3">
                    {[
                      { value: 'light', label: 'Light', icon: SunIcon },
                      { value: 'dark', label: 'Dark', icon: MoonIcon },
                      { value: 'auto', label: 'Auto', icon: ComputerDesktopIcon },
                    ].map(theme => (
                      <button
                        key={theme.value}
                        onClick={() => updatePreference('theme', theme.value as any)}
                        className={`p-3 border rounded-lg flex flex-col items-center gap-2 transition-colors ${
                          preferences.theme === theme.value
                            ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                            : 'border-slate-200 dark:border-slate-600 hover:border-primary-300'
                        }`}
                      >
                        <theme.icon className="w-5 h-5" />
                        <span className="text-sm">{theme.label}</span>
                      </button>
                    ))}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Show Advanced Filters
                    </label>
                    <p className="text-xs text-slate-500 dark:text-slate-400">
                      Display advanced filtering options by default
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={preferences.showAdvancedFilters}
                    onChange={e => updatePreference('showAdvancedFilters', e.target.checked)}
                    className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
                  />
                </div>
              </div>
            )}

            {/* Search Tab */}
            {activeTab === 'search' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
                  Search Settings
                </h3>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Enable Search Suggestions
                    </label>
                    <p className="text-xs text-slate-500 dark:text-slate-400">
                      Show suggestions based on search history
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={preferences.enableSearchSuggestions}
                    onChange={e => updatePreference('enableSearchSuggestions', e.target.checked)}
                    className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Search History Limit
                  </label>
                  <select
                    value={preferences.maxSearchHistory}
                    onChange={e => updatePreference('maxSearchHistory', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
                  >
                    <option value={25}>25 items</option>
                    <option value={50}>50 items</option>
                    <option value={100}>100 items</option>
                    <option value={200}>200 items</option>
                  </select>
                </div>

                <div>
                  <button
                    onClick={() => {
                      const userPrefs = getUserPreferences();
                      userPrefs.clearSearchHistory();
                      setPreferences(userPrefs.getPreferences());
                    }}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Clear Search History
                  </button>
                </div>
              </div>
            )}

            {/* Privacy Tab */}
            {activeTab === 'privacy' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
                  Privacy Settings
                </h3>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Enable Analytics
                    </label>
                    <p className="text-xs text-slate-500 dark:text-slate-400">
                      Help improve the app by sharing usage analytics
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={preferences.enableAnalytics}
                    onChange={e => updatePreference('enableAnalytics', e.target.checked)}
                    className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Share Usage Data
                    </label>
                    <p className="text-xs text-slate-500 dark:text-slate-400">
                      Share anonymized usage data to help improve features
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={preferences.shareUsageData}
                    onChange={e => updatePreference('shareUsageData', e.target.checked)}
                    className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-slate-200 dark:border-slate-700 p-6 flex justify-between">
          <div className="flex gap-3">
            <button
              onClick={handleExportPreferences}
              className="px-4 py-2 bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 rounded-lg hover:bg-slate-200 dark:hover:bg-slate-600 transition-colors"
            >
              Export Settings
            </button>
            <label className="px-4 py-2 bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 rounded-lg hover:bg-slate-200 dark:hover:bg-slate-600 transition-colors cursor-pointer">
              Import Settings
              <input
                type="file"
                accept=".json"
                onChange={handleImportPreferences}
                className="hidden"
              />
            </label>
            <button
              onClick={handleResetPreferences}
              className="px-4 py-2 bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/40 transition-colors"
            >
              Reset to Defaults
            </button>
          </div>
          <button
            onClick={onClose}
            className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            Done
          </button>
        </div>
      </div>
    </div>
  );
};
