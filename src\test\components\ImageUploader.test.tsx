import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi } from 'vitest';
import { ImageUploader } from '../../../components/ImageUploader';

describe('ImageUploader', () => {
  const mockOnImageUpload = vi.fn();

  beforeEach(() => {
    mockOnImageUpload.mockClear();
  });

  it('renders upload area correctly', () => {
    render(<ImageUploader onImageUpload={mockOnImageUpload} />);
    
    expect(screen.getByText('Click to upload')).toBeInTheDocument();
    expect(screen.getByText(/or drag and drop/)).toBeInTheDocument();
    expect(screen.getByText(/Max 10MB each/)).toBeInTheDocument();
  });

  it('shows maximum images limit', () => {
    render(<ImageUploader onImageUpload={mockOnImageUpload} maxImages={5} />);
    
    expect(screen.getByText(/Max 10MB each/)).toBeInTheDocument();
  });

  it('shows compression info when enabled', () => {
    render(<ImageUploader onImageUpload={mockOnImageUpload} enableCompression={true} />);
    
    expect(screen.getByText(/Auto-compressed/)).toBeInTheDocument();
  });

  it('shows custom file size limit', () => {
    render(<ImageUploader onImageUpload={mockOnImageUpload} maxFileSize={5} />);
    
    expect(screen.getByText(/Max 5MB each/)).toBeInTheDocument();
  });

  it('shows custom allowed types', () => {
    render(
      <ImageUploader 
        onImageUpload={mockOnImageUpload} 
        allowedTypes={['image/png', 'image/jpeg']}
      />
    );
    
    expect(screen.getByText(/PNG, JPEG/)).toBeInTheDocument();
  });

  it('has file input with correct attributes', () => {
    render(<ImageUploader onImageUpload={mockOnImageUpload} />);
    
    const fileInput = screen.getByRole('textbox', { hidden: true }) || 
                     document.querySelector('input[type="file"]');
    
    expect(fileInput).toHaveAttribute('multiple');
    expect(fileInput).toHaveAttribute('accept');
  });

  it('shows upload progress when uploading', () => {
    render(<ImageUploader onImageUpload={mockOnImageUpload} />);
    
    // This would require mocking file upload to test properly
    // For now, we just test that the component renders without errors
    expect(screen.getByText('Click to upload')).toBeInTheDocument();
  });

  it('handles drag and drop styling', async () => {
    const user = userEvent.setup();
    render(<ImageUploader onImageUpload={mockOnImageUpload} />);
    
    const uploadArea = screen.getByText('Click to upload').closest('label');
    expect(uploadArea).toBeInTheDocument();
    
    // Test that the component handles drag events without errors
    if (uploadArea) {
      await user.hover(uploadArea);
    }
  });

  it('shows error messages', () => {
    render(<ImageUploader onImageUpload={mockOnImageUpload} />);
    
    // The error state would be triggered by file validation
    // For now, we test that the component structure is correct
    expect(screen.getByText('Click to upload')).toBeInTheDocument();
  });

  it('respects maxImages prop', () => {
    render(<ImageUploader onImageUpload={mockOnImageUpload} maxImages={3} />);
    
    // When no images are uploaded, should show normal upload text
    expect(screen.getByText('Click to upload')).toBeInTheDocument();
  });
});
