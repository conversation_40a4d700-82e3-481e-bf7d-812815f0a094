import { getExpandedDatabase } from './expandedDatabaseService.ts';
import type { Device, Component } from '../types.ts';

export class ExportImportService {
  private db = getExpandedDatabase();

  // Export all data to JSON
  async exportAllData(): Promise<string> {
    try {
      const devices = await this.db.searchDevices({}, 10000, 0);
      const components = await this.db.searchComponents({}, 10000, 0);
      const stats = await this.db.getDatabaseStats();

      const exportData = {
        version: '1.0',
        exported_at: new Date().toISOString(),
        data: {
          devices,
          components,
          stats,
        },
      };

      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('Export failed:', error);
      throw new Error('Failed to export data');
    }
  }

  // Export devices only
  async exportDevices(): Promise<string> {
    try {
      const devices = await this.db.searchDevices({}, 10000, 0);

      const exportData = {
        version: '1.0',
        type: 'devices',
        exported_at: new Date().toISOString(),
        data: devices,
      };

      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('Device export failed:', error);
      throw new Error('Failed to export devices');
    }
  }

  // Export components only
  async exportComponents(): Promise<string> {
    try {
      const components = await this.db.searchComponents({}, 10000, 0);

      const exportData = {
        version: '1.0',
        type: 'components',
        exported_at: new Date().toISOString(),
        data: components,
      };

      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('Component export failed:', error);
      throw new Error('Failed to export components');
    }
  }

  // Export single item
  async exportItem(item: Device | Component): Promise<string> {
    try {
      const isDevice = 'category' in item;

      const exportData = {
        version: '1.0',
        type: isDevice ? 'device' : 'component',
        exported_at: new Date().toISOString(),
        data: item,
      };

      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('Item export failed:', error);
      throw new Error('Failed to export item');
    }
  }

  // Download file helper
  downloadFile(content: string, filename: string, contentType: string = 'application/json') {
    const blob = new Blob([content], { type: contentType });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(url);
  }

  // Export to CSV
  async exportDevicesCSV(): Promise<string> {
    try {
      const devices = await this.db.searchDevices({}, 10000, 0);

      if (devices.length === 0) {
        return 'No devices to export';
      }

      // Get all unique specification keys
      const allSpecKeys = new Set<string>();
      devices.forEach(device => {
        Object.keys(device.specifications).forEach(key => allSpecKeys.add(key));
      });

      const specKeys = Array.from(allSpecKeys).sort();

      // Create CSV header
      const headers = [
        'ID',
        'Category',
        'Brand',
        'Model',
        'Release Year',
        'Discontinued',
        'MSRP',
        ...specKeys.map(key => `Spec: ${key}`),
        'Created At',
        'Updated At',
      ];

      // Create CSV rows
      const rows = devices.map(device => [
        device.id || '',
        device.category,
        device.brand,
        device.model,
        device.release_year || '',
        device.discontinued ? 'Yes' : 'No',
        device.msrp || '',
        ...specKeys.map(key => device.specifications[key] || ''),
        device.created_at || '',
        device.updated_at || '',
      ]);

      // Combine headers and rows
      const csvContent = [headers, ...rows]
        .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
        .join('\n');

      return csvContent;
    } catch (error) {
      console.error('CSV export failed:', error);
      throw new Error('Failed to export devices to CSV');
    }
  }

  // Import data from JSON
  async importData(
    jsonContent: string
  ): Promise<{ success: boolean; imported: number; errors: string[] }> {
    const result = {
      success: true,
      imported: 0,
      errors: [] as string[],
    };

    try {
      const importData = JSON.parse(jsonContent);

      if (!importData.version || !importData.data) {
        throw new Error('Invalid import file format');
      }

      // Handle different import types
      if (
        importData.type === 'devices' ||
        (importData.data.devices && Array.isArray(importData.data.devices))
      ) {
        const devices = importData.data.devices || importData.data;

        for (const deviceData of devices) {
          try {
            // Remove ID and timestamps to avoid conflicts
            const { id, created_at, updated_at, ...cleanDeviceData } = deviceData;
            await this.db.saveDevice(cleanDeviceData);
            result.imported++;
          } catch (error) {
            result.errors.push(
              `Failed to import device ${deviceData.brand} ${deviceData.model}: ${error}`
            );
          }
        }
      }

      if (
        importData.type === 'components' ||
        (importData.data.components && Array.isArray(importData.data.components))
      ) {
        const components = importData.data.components || importData.data;

        for (const componentData of components) {
          try {
            // Remove ID and timestamps to avoid conflicts
            const { id, created_at, updated_at, ...cleanComponentData } = componentData;
            await this.db.saveComponent(cleanComponentData);
            result.imported++;
          } catch (error) {
            result.errors.push(
              `Failed to import component ${componentData.brand} ${componentData.model}: ${error}`
            );
          }
        }
      }

      // Handle single item import
      if (importData.type === 'device') {
        try {
          const { id, created_at, updated_at, ...cleanDeviceData } = importData.data;
          await this.db.saveDevice(cleanDeviceData);
          result.imported++;
        } catch (error) {
          result.errors.push(`Failed to import device: ${error}`);
        }
      }

      if (importData.type === 'component') {
        try {
          const { id, created_at, updated_at, ...cleanComponentData } = importData.data;
          await this.db.saveComponent(cleanComponentData);
          result.imported++;
        } catch (error) {
          result.errors.push(`Failed to import component: ${error}`);
        }
      }
    } catch (error) {
      result.success = false;
      result.errors.push(`Import failed: ${error}`);
    }

    return result;
  }

  // Validate import file
  validateImportFile(jsonContent: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    try {
      const data = JSON.parse(jsonContent);

      if (!data.version) {
        errors.push('Missing version field');
      }

      if (!data.data) {
        errors.push('Missing data field');
      }

      if (data.type && !['devices', 'components', 'device', 'component'].includes(data.type)) {
        errors.push('Invalid data type');
      }

      // Additional validation could be added here
    } catch (error) {
      errors.push('Invalid JSON format');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

// Singleton instance
let exportImportInstance: ExportImportService | null = null;

export function getExportImportService(): ExportImportService {
  if (!exportImportInstance) {
    exportImportInstance = new ExportImportService();
  }
  return exportImportInstance;
}
