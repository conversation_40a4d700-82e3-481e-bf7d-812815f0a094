
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Resale Assistant AI</title>
    <!-- Tailwind CDN removed: now using PostCSS build -->
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@18.3.1",
    "react-dom/client": "https://esm.sh/react-dom@18.3.1/client",
    "@google/genai": "https://esm.sh/@google/genai@0.14.0",
    "react-dom/": "https://esm.sh/react-dom@18.3.1/",
    "react/": "https://esm.sh/react@18.3.1/"
  }
}
</script>
<script src="https://unpkg.com/@babel/standalone@7.24.9/babel.min.js"></script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-slate-50 dark:bg-slate-900">
    <div id="root"></div>
    <!-- Only one entry script should be loaded to avoid duplicate mounting -->
    <script type="module" src="/index.tsx"></script>
</body>
</html>