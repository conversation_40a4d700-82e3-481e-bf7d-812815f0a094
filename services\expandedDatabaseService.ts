import initSqlJs from 'sql.js';
import type { <PERSON><PERSON>, Component, DeviceVariation, SearchFilter, DatabaseStats, DeviceCategory, ComponentType } from '../types.ts';

class ExpandedDatabaseService {
  private db: any;
  private isInitialized: boolean = false;

  constructor() {
    this.initializeDatabase();
  }

  private async initializeDatabase() {
    if (this.isInitialized) return;

    try {
      const SQL = await initSqlJs({
        locateFile: (file: string) => `https://sql.js.org/dist/${file}`
      });

      // Try to load existing database from localStorage
      const savedDb = localStorage.getItem('electronics_database');
      if (savedDb) {
        const uint8Array = new Uint8Array(JSON.parse(savedDb));
        this.db = new SQL.Database(uint8Array);
      } else {
        this.db = new SQL.Database();
      }

      this.createTables();
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize expanded database:', error);
      throw error;
    }
  }

  private async ensureInitialized() {
    if (!this.isInitialized) {
      await this.initializeDatabase();
    }
  }

  private saveToLocalStorage() {
    try {
      const data = this.db.export();
      const dataArray = Array.from(data);
      localStorage.setItem('electronics_database', JSON.stringify(dataArray));
    } catch (error) {
      console.error('Failed to save expanded database to localStorage:', error);
    }
  }

  private createTables() {
    // Devices table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS devices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category TEXT NOT NULL,
        brand TEXT NOT NULL,
        model TEXT NOT NULL,
        release_year INTEGER,
        discontinued BOOLEAN DEFAULT FALSE,
        msrp REAL,
        specifications TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Device variations table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS device_variations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        device_id INTEGER NOT NULL,
        variation_name TEXT NOT NULL,
        specifications TEXT NOT NULL,
        price_difference REAL,
        availability TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Components table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS components (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        brand TEXT NOT NULL,
        model TEXT NOT NULL,
        series TEXT,
        release_year INTEGER,
        discontinued BOOLEAN DEFAULT FALSE,
        msrp REAL,
        specifications TEXT NOT NULL,
        performance_metrics TEXT,
        compatibility TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Search cache table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS search_cache (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        search_query TEXT NOT NULL,
        search_type TEXT NOT NULL,
        result_count INTEGER DEFAULT 0,
        search_count INTEGER DEFAULT 1,
        last_searched DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create indexes
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_devices_category ON devices (category);
      CREATE INDEX IF NOT EXISTS idx_devices_brand_model ON devices (brand, model);
      CREATE INDEX IF NOT EXISTS idx_components_type ON components (type);
      CREATE INDEX IF NOT EXISTS idx_components_brand_model ON components (brand, model);
      CREATE INDEX IF NOT EXISTS idx_search_cache_query ON search_cache (search_query);
    `);
  }

  // Validation methods
  private validateDevice(device: Omit<Device, 'id' | 'created_at' | 'updated_at'>): string[] {
    const errors: string[] = [];

    if (!device.brand || device.brand.trim().length === 0) {
      errors.push('Brand is required');
    }

    if (!device.model || device.model.trim().length === 0) {
      errors.push('Model is required');
    }

    if (!device.category) {
      errors.push('Category is required');
    }

    if (!device.specifications || Object.keys(device.specifications).length === 0) {
      errors.push('At least one specification is required');
    }

    if (device.release_year && (device.release_year < 1900 || device.release_year > new Date().getFullYear() + 2)) {
      errors.push('Release year must be between 1900 and current year + 2');
    }

    if (device.msrp && device.msrp < 0) {
      errors.push('MSRP cannot be negative');
    }

    return errors;
  }

  private validateComponent(component: Omit<Component, 'id' | 'created_at' | 'updated_at'>): string[] {
    const errors: string[] = [];

    if (!component.brand || component.brand.trim().length === 0) {
      errors.push('Brand is required');
    }

    if (!component.model || component.model.trim().length === 0) {
      errors.push('Model is required');
    }

    if (!component.type) {
      errors.push('Component type is required');
    }

    if (!component.specifications || Object.keys(component.specifications).length === 0) {
      errors.push('At least one specification is required');
    }

    if (component.release_year && (component.release_year < 1900 || component.release_year > new Date().getFullYear() + 2)) {
      errors.push('Release year must be between 1900 and current year + 2');
    }

    if (component.msrp && component.msrp < 0) {
      errors.push('MSRP cannot be negative');
    }

    return errors;
  }

  private async checkForDuplicates(item: Device | Component): Promise<boolean> {
    try {
      if ('category' in item) {
        // Check for device duplicates
        const existing = await this.findDevice(item.brand, item.model, item.category);
        return existing !== null;
      } else {
        // Check for component duplicates
        const existing = await this.findComponent(item.brand, item.model, item.type);
        return existing !== null;
      }
    } catch (error) {
      console.error('Error checking for duplicates:', error);
      return false;
    }
  }

  // Device operations
  async saveDevice(device: Omit<Device, 'id' | 'created_at' | 'updated_at'>): Promise<Device> {
    await this.ensureInitialized();

    // Validate device data
    const validationErrors = this.validateDevice(device);
    if (validationErrors.length > 0) {
      throw new Error(`Device validation failed: ${validationErrors.join(', ')}`);
    }

    try {
      // Check if device already exists
      const existingResult = this.db.exec(`
        SELECT id FROM devices 
        WHERE LOWER(brand) = ? AND LOWER(model) = ? AND category = ?
      `, [device.brand.toLowerCase(), device.model.toLowerCase(), device.category]);

      let deviceId: number;

      if (existingResult.length > 0 && existingResult[0].values.length > 0) {
        // Update existing device
        deviceId = existingResult[0].values[0][0] as number;
        this.db.exec(`
          UPDATE devices 
          SET release_year = ?, discontinued = ?, msrp = ?, specifications = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `, [device.release_year, device.discontinued, device.msrp, JSON.stringify(device.specifications), deviceId]);

        // Delete existing variations
        this.db.exec(`DELETE FROM device_variations WHERE device_id = ?`, [deviceId]);
      } else {
        // Insert new device
        this.db.exec(`
          INSERT INTO devices (category, brand, model, release_year, discontinued, msrp, specifications)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [device.category, device.brand, device.model, device.release_year, device.discontinued, device.msrp, JSON.stringify(device.specifications)]);

        const idResult = this.db.exec(`SELECT last_insert_rowid()`);
        deviceId = idResult[0].values[0][0] as number;
      }

      // Insert variations if any
      if (device.variations) {
        for (const variation of device.variations) {
          this.db.exec(`
            INSERT INTO device_variations (device_id, variation_name, specifications, price_difference, availability)
            VALUES (?, ?, ?, ?, ?)
          `, [deviceId, variation.variation_name, JSON.stringify(variation.specifications), variation.price_difference, variation.availability]);
        }
      }

      this.saveToLocalStorage();

      return {
        id: deviceId,
        ...device,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error saving device:', error);
      throw error;
    }
  }

  async findDevice(brand: string, model: string, category?: DeviceCategory): Promise<Device | null> {
    await this.ensureInitialized();

    try {
      let query = `SELECT * FROM devices WHERE LOWER(brand) = ? AND LOWER(model) = ?`;
      let params = [brand.toLowerCase(), model.toLowerCase()];

      if (category) {
        query += ` AND category = ?`;
        params.push(category);
      }

      const result = this.db.exec(query, params);

      if (!result.length || !result[0].values.length) {
        return null;
      }

      const deviceRow = result[0].values[0];
      const device = {
        id: deviceRow[0],
        category: deviceRow[1],
        brand: deviceRow[2],
        model: deviceRow[3],
        release_year: deviceRow[4],
        discontinued: deviceRow[5],
        msrp: deviceRow[6],
        specifications: JSON.parse(deviceRow[7]),
        created_at: deviceRow[8],
        updated_at: deviceRow[9]
      };

      // Get variations
      const variationsResult = this.db.exec(`
        SELECT * FROM device_variations WHERE device_id = ? ORDER BY id
      `, [device.id]);

      const variations = variationsResult.length > 0 ? variationsResult[0].values.map((row: any) => ({
        id: row[0],
        device_id: row[1],
        variation_name: row[2],
        specifications: JSON.parse(row[3]),
        price_difference: row[4],
        availability: row[5],
        created_at: row[6]
      })) : [];

      return {
        ...device,
        variations
      } as Device;
    } catch (error) {
      console.error('Error finding device:', error);
      return null;
    }
  }

  // Component operations
  async saveComponent(component: Omit<Component, 'id' | 'created_at' | 'updated_at'>): Promise<Component> {
    await this.ensureInitialized();

    // Validate component data
    const validationErrors = this.validateComponent(component);
    if (validationErrors.length > 0) {
      throw new Error(`Component validation failed: ${validationErrors.join(', ')}`);
    }

    try {
      // Check if component already exists
      const existingResult = this.db.exec(`
        SELECT id FROM components 
        WHERE LOWER(brand) = ? AND LOWER(model) = ? AND type = ?
      `, [component.brand.toLowerCase(), component.model.toLowerCase(), component.type]);

      let componentId: number;

      if (existingResult.length > 0 && existingResult[0].values.length > 0) {
        // Update existing component
        componentId = existingResult[0].values[0][0] as number;
        this.db.exec(`
          UPDATE components 
          SET series = ?, release_year = ?, discontinued = ?, msrp = ?, specifications = ?, 
              performance_metrics = ?, compatibility = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `, [
          component.series, component.release_year, component.discontinued, component.msrp,
          JSON.stringify(component.specifications), JSON.stringify(component.performance_metrics),
          JSON.stringify(component.compatibility), componentId
        ]);
      } else {
        // Insert new component
        this.db.exec(`
          INSERT INTO components (type, brand, model, series, release_year, discontinued, msrp, 
                                specifications, performance_metrics, compatibility)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          component.type, component.brand, component.model, component.series, component.release_year,
          component.discontinued, component.msrp, JSON.stringify(component.specifications),
          JSON.stringify(component.performance_metrics), JSON.stringify(component.compatibility)
        ]);

        const idResult = this.db.exec(`SELECT last_insert_rowid()`);
        componentId = idResult[0].values[0][0] as number;
      }

      this.saveToLocalStorage();

      return {
        id: componentId,
        ...component,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error saving component:', error);
      throw error;
    }
  }

  async findComponent(brand: string, model: string, type?: ComponentType): Promise<Component | null> {
    await this.ensureInitialized();

    try {
      let query = `SELECT * FROM components WHERE LOWER(brand) = ? AND LOWER(model) = ?`;
      let params = [brand.toLowerCase(), model.toLowerCase()];

      if (type) {
        query += ` AND type = ?`;
        params.push(type);
      }

      const result = this.db.exec(query, params);

      if (!result.length || !result[0].values.length) {
        return null;
      }

      const componentRow = result[0].values[0];
      return {
        id: componentRow[0],
        type: componentRow[1],
        brand: componentRow[2],
        model: componentRow[3],
        series: componentRow[4],
        release_year: componentRow[5],
        discontinued: componentRow[6],
        msrp: componentRow[7],
        specifications: JSON.parse(componentRow[8]),
        performance_metrics: componentRow[9] ? JSON.parse(componentRow[9]) : null,
        compatibility: componentRow[10] ? JSON.parse(componentRow[10]) : null,
        created_at: componentRow[11],
        updated_at: componentRow[12]
      } as Component;
    } catch (error) {
      console.error('Error finding component:', error);
      return null;
    }
  }

  // Search operations
  async searchDevices(filter: SearchFilter, limit: number = 50, offset: number = 0): Promise<Device[]> {
    await this.ensureInitialized();

    try {
      let query = `SELECT * FROM devices WHERE 1=1`;
      let params: any[] = [];

      if (filter.category && filter.category.length > 0) {
        query += ` AND category IN (${filter.category.map(() => '?').join(',')})`;
        params.push(...filter.category);
      }

      if (filter.brand && filter.brand.length > 0) {
        query += ` AND LOWER(brand) IN (${filter.brand.map(() => '?').join(',')})`;
        params.push(...filter.brand.map(b => b.toLowerCase()));
      }

      if (filter.text_search) {
        query += ` AND (LOWER(brand) LIKE ? OR LOWER(model) LIKE ? OR LOWER(specifications) LIKE ?)`;
        const searchTerm = `%${filter.text_search.toLowerCase()}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      if (filter.price_range) {
        query += ` AND msrp BETWEEN ? AND ?`;
        params.push(filter.price_range[0], filter.price_range[1]);
      }

      if (filter.release_year_range) {
        query += ` AND release_year BETWEEN ? AND ?`;
        params.push(filter.release_year_range[0], filter.release_year_range[1]);
      }

      query += ` ORDER BY updated_at DESC LIMIT ? OFFSET ?`;
      params.push(limit, offset);

      const result = this.db.exec(query, params);

      if (!result.length) return [];

      return result[0].values.map((row: any) => ({
        id: row[0],
        category: row[1],
        brand: row[2],
        model: row[3],
        release_year: row[4],
        discontinued: row[5],
        msrp: row[6],
        specifications: JSON.parse(row[7]),
        created_at: row[8],
        updated_at: row[9]
      }));
    } catch (error) {
      console.error('Error searching devices:', error);
      return [];
    }
  }

  async searchComponents(filter: SearchFilter, limit: number = 50, offset: number = 0): Promise<Component[]> {
    await this.ensureInitialized();

    try {
      let query = `SELECT * FROM components WHERE 1=1`;
      let params: any[] = [];

      if (filter.component_type && filter.component_type.length > 0) {
        query += ` AND type IN (${filter.component_type.map(() => '?').join(',')})`;
        params.push(...filter.component_type);
      }

      if (filter.brand && filter.brand.length > 0) {
        query += ` AND LOWER(brand) IN (${filter.brand.map(() => '?').join(',')})`;
        params.push(...filter.brand.map(b => b.toLowerCase()));
      }

      if (filter.text_search) {
        query += ` AND (LOWER(brand) LIKE ? OR LOWER(model) LIKE ? OR LOWER(specifications) LIKE ?)`;
        const searchTerm = `%${filter.text_search.toLowerCase()}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      if (filter.price_range) {
        query += ` AND msrp BETWEEN ? AND ?`;
        params.push(filter.price_range[0], filter.price_range[1]);
      }

      if (filter.release_year_range) {
        query += ` AND release_year BETWEEN ? AND ?`;
        params.push(filter.release_year_range[0], filter.release_year_range[1]);
      }

      query += ` ORDER BY updated_at DESC LIMIT ? OFFSET ?`;
      params.push(limit, offset);

      const result = this.db.exec(query, params);

      if (!result.length) return [];

      return result[0].values.map((row: any) => ({
        id: row[0],
        type: row[1],
        brand: row[2],
        model: row[3],
        series: row[4],
        release_year: row[5],
        discontinued: row[6],
        msrp: row[7],
        specifications: JSON.parse(row[8]),
        performance_metrics: row[9] ? JSON.parse(row[9]) : null,
        compatibility: row[10] ? JSON.parse(row[10]) : null,
        created_at: row[11],
        updated_at: row[12]
      }));
    } catch (error) {
      console.error('Error searching components:', error);
      return [];
    }
  }

  async getDatabaseStats(): Promise<DatabaseStats> {
    await this.ensureInitialized();

    try {
      // Total devices
      const totalDevicesResult = this.db.exec(`SELECT COUNT(*) FROM devices`);
      const totalDevices = totalDevicesResult[0].values[0][0] as number;

      // Total components
      const totalComponentsResult = this.db.exec(`SELECT COUNT(*) FROM components`);
      const totalComponents = totalComponentsResult[0].values[0][0] as number;

      // Devices by category
      const devicesByCategoryResult = this.db.exec(`
        SELECT category, COUNT(*) FROM devices GROUP BY category
      `);
      const devicesByCategory: Record<DeviceCategory, number> = {} as any;
      if (devicesByCategoryResult.length > 0) {
        devicesByCategoryResult[0].values.forEach((row: any) => {
          devicesByCategory[row[0] as DeviceCategory] = row[1];
        });
      }

      // Components by type
      const componentsByTypeResult = this.db.exec(`
        SELECT type, COUNT(*) FROM components GROUP BY type
      `);
      const componentsByType: Record<ComponentType, number> = {} as any;
      if (componentsByTypeResult.length > 0) {
        componentsByTypeResult[0].values.forEach((row: any) => {
          componentsByType[row[0] as ComponentType] = row[1];
        });
      }

      // Recent additions (last 7 days)
      const recentAdditionsResult = this.db.exec(`
        SELECT COUNT(*) FROM (
          SELECT created_at FROM devices WHERE created_at > datetime('now', '-7 days')
          UNION ALL
          SELECT created_at FROM components WHERE created_at > datetime('now', '-7 days')
        )
      `);
      const recentAdditions = recentAdditionsResult[0].values[0][0] as number;

      // Popular searches
      const popularSearchesResult = this.db.exec(`
        SELECT search_query, search_count FROM search_cache
        ORDER BY search_count DESC LIMIT 10
      `);
      const popularSearches = popularSearchesResult.length > 0
        ? popularSearchesResult[0].values.map((row: any) => ({
            query: row[0],
            count: row[1]
          }))
        : [];

      return {
        total_devices: totalDevices,
        total_components: totalComponents,
        devices_by_category: devicesByCategory,
        components_by_type: componentsByType,
        recent_additions: recentAdditions,
        popular_searches: popularSearches
      };
    } catch (error) {
      console.error('Error getting database stats:', error);
      return {
        total_devices: 0,
        total_components: 0,
        devices_by_category: {} as any,
        components_by_type: {} as any,
        recent_additions: 0,
        popular_searches: []
      };
    }
  }

  async updateSearchCache(query: string, type: string, resultCount: number) {
    await this.ensureInitialized();

    try {
      const existingResult = this.db.exec(`
        SELECT search_count FROM search_cache WHERE search_query = ? AND search_type = ?
      `, [query, type]);

      if (existingResult.length > 0 && existingResult[0].values.length > 0) {
        this.db.exec(`
          UPDATE search_cache
          SET search_count = search_count + 1, result_count = ?, last_searched = CURRENT_TIMESTAMP
          WHERE search_query = ? AND search_type = ?
        `, [resultCount, query, type]);
      } else {
        this.db.exec(`
          INSERT INTO search_cache (search_query, search_type, result_count, search_count, last_searched)
          VALUES (?, ?, ?, 1, CURRENT_TIMESTAMP)
        `, [query, type, resultCount]);
      }

      this.saveToLocalStorage();
    } catch (error) {
      console.error('Error updating search cache:', error);
    }
  }

  // Search indexing for better performance
  private createSearchIndex(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2)
      .filter((word, index, arr) => arr.indexOf(word) === index); // Remove duplicates
  }

  private buildSearchableText(item: Device | Component): string {
    const isDevice = 'category' in item;

    let searchText = `${item.brand} ${item.model}`;

    if (isDevice) {
      searchText += ` ${(item as Device).category}`;
    } else {
      searchText += ` ${(item as Component).type}`;
      if ((item as Component).series) {
        searchText += ` ${(item as Component).series}`;
      }
    }

    // Add specifications to search text
    Object.entries(item.specifications).forEach(([key, value]) => {
      searchText += ` ${key} ${value}`;
    });

    // Add performance metrics for components
    if (!isDevice && (item as Component).performance_metrics) {
      Object.entries((item as Component).performance_metrics!).forEach(([key, value]) => {
        searchText += ` ${key} ${value}`;
      });
    }

    return searchText;
  }

  async performFullTextSearch(query: string, type: 'devices' | 'components' | 'all' = 'all', limit: number = 50): Promise<{devices: Device[], components: Component[]}> {
    await this.ensureInitialized();

    const searchTerms = this.createSearchIndex(query);
    if (searchTerms.length === 0) {
      return { devices: [], components: [] };
    }

    const results = { devices: [] as Device[], components: [] as Component[] };

    try {
      if (type === 'devices' || type === 'all') {
        const allDevices = await this.searchDevices({}, 10000, 0);

        const scoredDevices = allDevices.map(device => {
          const searchableText = this.buildSearchableText(device);
          const searchWords = this.createSearchIndex(searchableText);

          let score = 0;
          searchTerms.forEach(term => {
            const exactMatches = searchWords.filter(word => word === term).length;
            const partialMatches = searchWords.filter(word => word.includes(term)).length;
            score += exactMatches * 3 + partialMatches;
          });

          return { device, score };
        });

        results.devices = scoredDevices
          .filter(item => item.score > 0)
          .sort((a, b) => b.score - a.score)
          .slice(0, limit)
          .map(item => item.device);
      }

      if (type === 'components' || type === 'all') {
        const allComponents = await this.searchComponents({}, 10000, 0);

        const scoredComponents = allComponents.map(component => {
          const searchableText = this.buildSearchableText(component);
          const searchWords = this.createSearchIndex(searchableText);

          let score = 0;
          searchTerms.forEach(term => {
            const exactMatches = searchWords.filter(word => word === term).length;
            const partialMatches = searchWords.filter(word => word.includes(term)).length;
            score += exactMatches * 3 + partialMatches;
          });

          return { component, score };
        });

        results.components = scoredComponents
          .filter(item => item.score > 0)
          .sort((a, b) => b.score - a.score)
          .slice(0, limit)
          .map(item => item.component);
      }

      // Update search cache
      await this.updateSearchCache(query, type, results.devices.length + results.components.length);

    } catch (error) {
      console.error('Full-text search failed:', error);
    }

    return results;
  }

  close() {
    if (this.db) {
      this.saveToLocalStorage();
      this.db.close();
    }
  }
}

// Singleton instance
let expandedDbInstance: ExpandedDatabaseService | null = null;

export function getExpandedDatabase(): ExpandedDatabaseService {
  if (!expandedDbInstance) {
    expandedDbInstance = new ExpandedDatabaseService();
  }
  return expandedDbInstance;
}

export { ExpandedDatabaseService };
