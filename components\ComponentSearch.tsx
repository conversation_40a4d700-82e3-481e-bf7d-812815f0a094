import React, { useState } from 'react';
import { ComponentType, Component } from '../types.ts';
import { MagnifyingGlassIcon, CpuChipIcon, CheckIcon } from './icons.tsx';

interface ComponentSearchProps {
  onComponentFound: (component: Component) => void;
}

const componentTypes: Array<{ value: ComponentType; label: string }> = [
  { value: 'cpu', label: 'CPU / Processor' },
  { value: 'gpu', label: 'GPU / Graphics Card' },
  { value: 'ram', label: 'RAM / Memory' },
  { value: 'storage', label: 'Storage (SSD/HDD)' },
  { value: 'motherboard', label: 'Motherboard' },
  { value: 'psu', label: 'Power Supply' },
  { value: 'cooling', label: 'Cooling System' },
  { value: 'case', label: 'PC Case' },
  { value: 'monitor', label: 'Monitor' },
  { value: 'keyboard', label: 'Keyboard' },
  { value: 'mouse', label: 'Mouse' },
  { value: 'headset', label: 'Headset' },
  { value: 'speaker', label: 'Speakers' },
  { value: 'webcam', label: 'Webcam' },
];

export const ComponentSearch: React.FC<ComponentSearchProps> = ({ onComponentFound }) => {
  const [componentType, setComponentType] = useState<ComponentType>('gpu');
  const [brand, setBrand] = useState('');
  const [model, setModel] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResult, setSearchResult] = useState<Component | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [searchSource, setSearchSource] = useState<'database' | 'api' | null>(null);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!brand.trim() || !model.trim()) {
      setError('Please enter both brand and model.');
      return;
    }

    setIsSearching(true);
    setError(null);
    setSearchResult(null);

    try {
      const { lookupComponentSpecs } = await import('../services/geminiService.ts');
      const result = await lookupComponentSpecs(brand.trim(), model.trim(), componentType);

      if (result.found && result.component) {
        setSearchResult(result.component);
        setSearchSource(result.source);
      } else {
        setError('Component not found. Please check the brand, model, and component type.');
      }
    } catch (err) {
      console.error('Component lookup error:', err);
      setError('An error occurred while searching for component specifications. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const handleUseComponent = () => {
    if (searchResult) {
      onComponentFound(searchResult);
    }
  };

  const formatSpecifications = (specs: Record<string, any>) => {
    return Object.entries(specs).map(([key, value]) => (
      <div key={key} className="flex justify-between text-sm py-1">
        <span className="text-slate-600 dark:text-slate-400 capitalize">
          {key.replace(/_/g, ' ')}:
        </span>
        <span className="text-slate-800 dark:text-slate-200 font-medium">
          {Array.isArray(value) ? value.join(', ') : String(value)}
        </span>
      </div>
    ));
  };

  const formatPerformanceMetrics = (metrics: Record<string, any> | null) => {
    if (!metrics) return null;

    return Object.entries(metrics).map(([key, value]) => (
      <div key={key} className="flex justify-between text-sm py-1">
        <span className="text-slate-600 dark:text-slate-400 capitalize">
          {key.replace(/_/g, ' ')}:
        </span>
        <span className="text-slate-800 dark:text-slate-200 font-medium">{String(value)}</span>
      </div>
    ));
  };

  return (
    <div className="bg-white dark:bg-slate-800 p-6 sm:p-8 rounded-2xl shadow-lg">
      <div className="text-center mb-6">
        <CpuChipIcon className="w-12 h-12 text-primary-500 mx-auto mb-3" />
        <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-100">Component Lookup</h2>
        <p className="text-slate-600 dark:text-slate-300 mt-2">
          Find detailed specifications for computer components
        </p>
      </div>

      {/* Search Form */}
      <form onSubmit={handleSearch} className="space-y-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Component Type *
          </label>
          <select
            value={componentType}
            onChange={e => setComponentType(e.target.value as ComponentType)}
            className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
            disabled={isSearching}
          >
            {componentTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Brand *
            </label>
            <input
              type="text"
              value={brand}
              onChange={e => setBrand(e.target.value)}
              placeholder="e.g., NVIDIA, Intel, AMD, Corsair"
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
              disabled={isSearching}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Model *
            </label>
            <input
              type="text"
              value={model}
              onChange={e => setModel(e.target.value)}
              placeholder="e.g., RTX 4080, i7-13700K, Vengeance LPX"
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
              disabled={isSearching}
            />
          </div>
        </div>

        {error && <p className="text-sm text-red-500 dark:text-red-400 text-center">{error}</p>}

        <div className="pt-2">
          <button
            type="submit"
            disabled={isSearching || !brand.trim() || !model.trim()}
            className="w-full flex items-center justify-center gap-2 bg-primary-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-4 focus:ring-primary-500/50 transition-all disabled:bg-slate-400 dark:disabled:bg-slate-600 disabled:cursor-not-allowed"
          >
            <MagnifyingGlassIcon className="w-5 h-5" />
            {isSearching ? 'Searching...' : 'Find Component'}
          </button>
        </div>
      </form>

      {/* Search Results */}
      {searchResult && (
        <div className="border-t border-slate-200 dark:border-slate-700 pt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
              {searchResult.brand} {searchResult.model}
            </h3>
            <div className="flex items-center gap-2">
              <span
                className={`text-xs px-2 py-1 rounded-full ${
                  searchSource === 'database'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                    : 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300'
                }`}
              >
                {searchSource === 'database' ? 'Cached' : 'Live Search'}
              </span>
              <span className="text-xs text-slate-500 dark:text-slate-400 capitalize">
                {searchResult.type}
              </span>
              {searchResult.release_year && (
                <span className="text-xs text-slate-500 dark:text-slate-400">
                  {searchResult.release_year}
                </span>
              )}
            </div>
          </div>

          <div className="bg-slate-50 dark:bg-slate-700/50 p-4 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Specifications */}
              <div>
                <h4 className="font-medium text-slate-700 dark:text-slate-300 mb-3">
                  Specifications
                </h4>
                <div className="space-y-1">{formatSpecifications(searchResult.specifications)}</div>
              </div>

              {/* Performance Metrics */}
              {searchResult.performance_metrics && (
                <div>
                  <h4 className="font-medium text-slate-700 dark:text-slate-300 mb-3">
                    Performance Metrics
                  </h4>
                  <div className="space-y-1">
                    {formatPerformanceMetrics(searchResult.performance_metrics)}
                  </div>
                </div>
              )}
            </div>

            {/* Compatibility */}
            {searchResult.compatibility && searchResult.compatibility.length > 0 && (
              <div className="mt-4 pt-4 border-t border-slate-200 dark:border-slate-600">
                <h4 className="font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Compatibility
                </h4>
                <div className="flex flex-wrap gap-2">
                  {searchResult.compatibility.map((item, index) => (
                    <span
                      key={index}
                      className="text-xs bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300 px-2 py-1 rounded"
                    >
                      {item}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Pricing */}
            {searchResult.msrp && (
              <div className="mt-4 pt-4 border-t border-slate-200 dark:border-slate-600">
                <div className="flex justify-between items-center">
                  <span className="text-slate-600 dark:text-slate-400">MSRP:</span>
                  <span className="font-bold text-green-600 dark:text-green-400 text-lg">
                    ${searchResult.msrp}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Use Component Button */}
          <div className="mt-6 pt-4 border-t border-slate-200 dark:border-slate-700">
            <button
              onClick={handleUseComponent}
              className="w-full bg-green-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-4 focus:ring-green-500/50 transition-all flex items-center justify-center gap-2"
            >
              <CheckIcon className="w-5 h-5" />
              Add to Database
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
