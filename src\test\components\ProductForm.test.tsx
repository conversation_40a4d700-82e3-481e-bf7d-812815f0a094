import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi } from 'vitest';
import { ProductForm } from '../../../components/ProductForm';

describe('ProductForm', () => {
  const mockOnAnalyze = vi.fn();

  beforeEach(() => {
    mockOnAnalyze.mockClear();
  });

  it('renders form elements correctly', () => {
    render(<ProductForm onAnalyze={mockOnAnalyze} />);

    expect(screen.getByText('1. Upload Product Images')).toBeInTheDocument();
    expect(screen.getByText('2. Describe Your Item')).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /analyze product/i })).toBeInTheDocument();
  });

  it('form validation works correctly', () => {
    render(<ProductForm onAnalyze={mockOnAnalyze} />);

    const submitButton = screen.getByRole('button', { name: /analyze product/i });

    // Button should be disabled initially (no images and no description)
    expect(submitButton).toBeDisabled();
  });

  it('has proper form structure', () => {
    render(<ProductForm onAnalyze={mockOnAnalyze} />);

    expect(screen.getByText('1. Upload Product Images')).toBeInTheDocument();
    expect(screen.getByText('2. Describe Your Item')).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/e.g., 'Old film camera/)).toBeInTheDocument();
  });

  it('updates description when user types', async () => {
    const user = userEvent.setup();
    render(<ProductForm onAnalyze={mockOnAnalyze} />);

    const textarea = screen.getByRole('textbox');
    await user.type(textarea, 'Test product description');

    expect(textarea).toHaveValue('Test product description');
  });
});
