import { GoogleGenAI } from '@google/genai';
import type {
  ProductAnalysis,
  GroundingSource,
  ComputerSpecs,
  ComputerModel,
  ComputerLookupResult,
  Device,
  Component,
  DeviceCategory,
  ComponentType,
} from '../types.ts';
import { getDatabase } from './databaseService.ts';
import { getExpandedDatabase } from './expandedDatabaseService.ts';

function base64ToInlineData(base64String: string) {
  const match = base64String.match(/^data:(image\/(?:jpeg|png|webp));base64,(.*)$/);
  if (!match) {
    // Fallback for strings that might be missing the prefix
    console.warn('Base64 string is missing the data URI prefix. Assuming image/jpeg.');
    return {
      inlineData: {
        mimeType: 'image/jpeg',
        data: base64String,
      },
    };
  }
  return {
    inlineData: {
      mimeType: match[1],
      data: match[2],
    },
  };
}

const jsonStructure = `{
  "productName": "A concise name for the identified product.",
  "suggestedTitle": "A compelling, SEO-friendly title for an online marketplace listing.",
  "detailedDescription": "A detailed product description suitable for a marketplace listing.",
  "keywords": ["A list of 5-10 relevant keywords."],
  "pricing": {
    "quickSale": {"price": 100, "justification": "Justification based on search results."},
    "fairMarket": {"price": 150, "justification": "Justification based on search results."},
    "ambitious": {"price": 200, "justification": "Justification based on search results."}
  },
  "searchResults": [
    {
      "title": "Exact title of the listing found",
      "url": "https://www.ebay.com/itm/123456789",
      "price": "$50 or price range like $45-55",
      "platform": "eBay, Facebook Marketplace, Craigslist, etc.",
      "condition": "Used, Like New, Fair, etc.",
      "description": "Brief description of what you found in this listing"
    }
  ]
}`;

const systemInstruction = `You are an expert e-commerce assistant specializing in the used goods market. Your task is to analyze one or more images of a product and a user-provided description to help the user create a compelling online listing.

CRITICAL REQUIREMENT: You MUST perform Google Search to find comparable listings. This is absolutely mandatory - do not proceed without search results.

IMPORTANT: When providing URLs in searchResults, include the URLs to the listings you found during your search.

1.  **Identify the Product:** From the provided images and text, identify the product, including brand and model if possible.
2.  **Create a Title:** Generate a catchy, descriptive title optimized for search engines.
3.  **Write a Description:** Draft a detailed, professional description. Mention that it's a used item and describe its potential features and benefits. Do not make up condition details not visible in the images.
4.  **Suggest Keywords:** Provide a list of relevant keywords for tagging the listing.
5.  **MANDATORY PRICING RESEARCH:** You MUST use Google Search to find comparable current and sold listings for this exact item in used condition. Search for multiple variations including:
   - "[Brand] [Model] used"
   - "[Brand] [Model] sold listings"
   - "[Product type] [Brand] [Model] price"
   - "[Product name] marketplace price"

   For EACH search result you find, you MUST extract and include in the searchResults array:
   - The exact title of the listing
   - The URL to the listing
   - The price (exact amount or range like "$45-55")
   - The platform (eBay, Facebook Marketplace, etc.)
   - The condition mentioned (Used, Like New, Fair, etc.)
   - A brief description of what you found

   If you cannot find search results, you MUST indicate this clearly by returning an empty searchResults array.

   Based on your search results, suggest three pricing tiers in USD: 'Quick Sale', 'Fair Market', and 'Ambitious'. Each justification MUST cite specific search results with examples like: 'Based on 3 sold eBay listings ranging $45-55, and 2 current listings at $60-70, a fair market price is...'

Your response MUST be a single, valid JSON object that strictly adheres to the structure provided below. Do not include any text, markdown, or formatting like \\\`\\\`\\\`json before or after the JSON object.
${jsonStructure}`;

const retrySystemInstruction = `You are an expert e-commerce assistant. You FAILED to provide search results in your previous attempt. This is CRITICAL - you MUST use Google Search to find pricing data.

MANDATORY: Perform Google Search RIGHT NOW for the product in the images. Search for:
- Used/sold listings on eBay, Facebook Marketplace, Craigslist
- Current market prices on multiple platforms
- Similar items with comparable features

For EACH search result you find, you MUST include it in the searchResults array with:
- Exact title, URL, price, platform, condition, and description

Do not respond without actual search results. If you truly cannot find any search results, return an empty searchResults array and state this explicitly in the pricing justifications.

Follow the same format as before, but ensure you actually perform searches and populate the searchResults array.
${jsonStructure}`;

async function attemptAnalysis(
  ai: any,
  imageParts: any[],
  textPart: any,
  instruction: string,
  attemptNumber: number
): Promise<{ response: any; sources: GroundingSource[] }> {
  console.log(`Attempt ${attemptNumber}: Requesting product analysis with search...`);

  const response = await ai.models.generateContent({
    model: 'gemini-2.5-flash',
    contents: { parts: [textPart, ...imageParts] },
    config: {
      systemInstruction: instruction,
      temperature: 0.3,
      tools: [{ googleSearch: {} }],
    },
  });

  const groundingChunks = response.candidates?.[0]?.groundingMetadata?.groundingChunks ?? [];
  console.log(`Attempt ${attemptNumber}: Found ${groundingChunks.length} grounding chunks`);

  const sources: GroundingSource[] = groundingChunks
    .map((chunk: any) => chunk.web)
    .filter((web: any): web is { uri: string; title: string } => !!(web?.uri && web.title))
    .map((web: any) => ({ uri: web.uri, title: web.title }));

  const uniqueSources = Array.from(new Map(sources.map(s => [s.uri, s])).values());
  console.log(`Attempt ${attemptNumber}: Extracted ${uniqueSources.length} unique sources`);

  return { response, sources: uniqueSources };
}

export async function identifyAndPriceProduct(
  imageBases64: string[],
  userDescription: string
): Promise<ProductAnalysis> {
  const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

  const imageParts = imageBases64.map(base64ToInlineData);
  const textPart = {
    text: `User's notes about the item: "${userDescription}"`,
  };

  let lastResponse: any;
  let sources: GroundingSource[] = [];

  // Attempt 1: Standard instruction
  try {
    const result = await attemptAnalysis(ai, imageParts, textPart, systemInstruction, 1);
    lastResponse = result.response;
    sources = result.sources;

    // Check if we got search results in the JSON response (parse it first to check)
    let hasSearchResults = false;
    try {
      const responseText = result.response.text;
      const jsonStart = responseText.indexOf('{');
      const jsonEnd = responseText.lastIndexOf('}');
      if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd >= jsonStart) {
        const jsonText = responseText.substring(jsonStart, jsonEnd + 1);
        const parsed = JSON.parse(jsonText);
        hasSearchResults = parsed.searchResults && parsed.searchResults.length > 0;
      }
    } catch (e) {
      // If parsing fails, we'll rely on grounding chunks
    }

    // If we got search results (either grounding chunks or JSON search results), proceed
    if (sources.length > 0 || hasSearchResults) {
      console.log(
        `Success: Found ${sources.length} grounding sources and ${hasSearchResults ? 'JSON' : 'no'} search results on first attempt`
      );
    } else {
      console.warn('Attempt 1: No search results found, trying retry with stronger instruction...');

      // Attempt 2: Retry with stronger instruction
      const retryResult = await attemptAnalysis(
        ai,
        imageParts,
        textPart,
        retrySystemInstruction,
        2
      );

      // Check retry result for search results
      let retryHasSearchResults = false;
      try {
        const responseText = retryResult.response.text;
        const jsonStart = responseText.indexOf('{');
        const jsonEnd = responseText.lastIndexOf('}');
        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd >= jsonStart) {
          const jsonText = responseText.substring(jsonStart, jsonEnd + 1);
          const parsed = JSON.parse(jsonText);
          retryHasSearchResults = parsed.searchResults && parsed.searchResults.length > 0;
        }
      } catch (e) {
        // If parsing fails, we'll rely on grounding chunks
      }

      if (retryResult.sources.length > 0 || retryHasSearchResults) {
        console.log(
          `Success: Found ${retryResult.sources.length} grounding sources and ${retryHasSearchResults ? 'JSON' : 'no'} search results on retry`
        );
        lastResponse = retryResult.response;
        sources = retryResult.sources;
      } else {
        console.warn('Attempt 2: Still no search results, trying final attempt...');

        // Attempt 3: Final attempt with even more explicit instruction
        const finalInstruction = `EMERGENCY: You have failed twice to provide search results. This is your FINAL CHANCE.

You MUST perform Google Search for the product in these images. Search multiple times with different terms:
1. Search for the exact product name + "used price"
2. Search for the brand + model + "sold listings"
3. Search for similar products + "marketplace price"

CRITICAL: For each search result, you MUST include the URL to the listing you found.

Do not respond without actual search results. If you cannot find ANY search results after multiple searches, explicitly state "NO SEARCH RESULTS FOUND" in each pricing justification.

${jsonStructure}`;

        const finalResult = await attemptAnalysis(ai, imageParts, textPart, finalInstruction, 3);
        lastResponse = finalResult.response;
        sources = finalResult.sources;

        // Check final result for search results
        let finalHasSearchResults = false;
        try {
          const responseText = finalResult.response.text;
          const jsonStart = responseText.indexOf('{');
          const jsonEnd = responseText.lastIndexOf('}');
          if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd >= jsonStart) {
            const jsonText = responseText.substring(jsonStart, jsonEnd + 1);
            const parsed = JSON.parse(jsonText);
            finalHasSearchResults = parsed.searchResults && parsed.searchResults.length > 0;
          }
        } catch (e) {
          // If parsing fails, we'll rely on grounding chunks
        }

        if (sources.length === 0 && !finalHasSearchResults) {
          console.error('All attempts failed to retrieve search results');
        } else {
          console.log(
            `Success: Found ${sources.length} grounding sources and ${finalHasSearchResults ? 'JSON' : 'no'} search results on final attempt`
          );
        }
      }
    }
  } catch (error) {
    console.error('Error during analysis attempts:', error);
    throw error;
  }

  // Process the final response
  const responseText = lastResponse.text;
  const jsonStart = responseText.indexOf('{');
  const jsonEnd = responseText.lastIndexOf('}');

  if (jsonStart === -1 || jsonEnd === -1 || jsonEnd < jsonStart) {
    console.error('Failed to find JSON object in response:', responseText);
    throw new Error('AI returned an invalid response. Please try again.');
  }

  const jsonText = responseText.substring(jsonStart, jsonEnd + 1);

  try {
    const analysisPart: Omit<ProductAnalysis, 'sources'> = JSON.parse(jsonText);

    // Ensure searchResults exists, default to empty array if not provided
    const searchResults = analysisPart.searchResults || [];

    // Basic validation - just ensure we have required fields
    const validSearchResults = searchResults.filter(result => {
      const isValid = result.url && result.title && result.price;
      if (!isValid) {
        console.warn(`Filtered out incomplete search result: ${JSON.stringify(result)}`);
      }
      return isValid;
    });

    console.log(
      `Search results validation: ${searchResults.length} -> ${validSearchResults.length} valid results`
    );

    // If no search results were found after all attempts, modify the pricing justifications
    if (sources.length === 0 && validSearchResults.length === 0) {
      console.warn('No search results found after all attempts - updating pricing justifications');
      analysisPart.pricing = {
        quickSale: {
          price: 0,
          justification:
            'Unable to determine pricing - no comparable listings found in search results. Please research manually.',
        },
        fairMarket: {
          price: 0,
          justification:
            'Unable to determine pricing - no comparable listings found in search results. Please research manually.',
        },
        ambitious: {
          price: 0,
          justification:
            'Unable to determine pricing - no comparable listings found in search results. Please research manually.',
        },
      };
    }

    const result: ProductAnalysis = {
      ...analysisPart,
      sources: sources,
      searchResults: validSearchResults,
    };

    console.log(
      `Final result: ${sources.length} sources, ${validSearchResults.length} valid search results, product: ${result.productName}`
    );
    return result;
  } catch (e) {
    console.error('Failed to parse JSON response:', jsonText);
    // Always return a fallback result so the UI can display an error
    return {
      productName: 'Unknown Product',
      suggestedTitle: 'No title available',
      detailedDescription: 'No description available. Gemini did not return valid results.',
      keywords: [],
      pricing: {
        quickSale: { price: 0, justification: 'No pricing available due to parsing error.' },
        fairMarket: { price: 0, justification: 'No pricing available due to parsing error.' },
        ambitious: { price: 0, justification: 'No pricing available due to parsing error.' },
      },
      sources: [],
      searchResults: [],
    };
  }
}

const computerSystemInstruction = `You are an expert computer hardware specialist and e-commerce assistant. Your task is to analyze computer specifications provided by the user and help them create a compelling online listing with accurate pricing.

CRITICAL REQUIREMENT: You MUST perform Google Search to find comparable computer listings. This is absolutely mandatory - do not proceed without search results.

IMPORTANT: When providing URLs in searchResults, include the URLs to the listings you found during your search.

1.  **Identify the Computer:** From the provided specifications, identify the exact computer model and its key components.
2.  **Create a Title:** Generate a catchy, descriptive title optimized for search engines that includes key specs.
3.  **Write a Description:** Draft a detailed, professional description highlighting the computer's capabilities, performance tier, and suitability for different use cases (gaming, productivity, etc.). Mention that it's a used item and describe its condition.
4.  **Suggest Keywords:** Provide a list of relevant keywords including brand, model, key components, and use cases.
5.  **MANDATORY PRICING RESEARCH:** You MUST use Google Search to find comparable computer listings. Search for multiple variations including:
   - "[Brand] [Model] used"
   - "[Brand] [Model] [Processor] sold listings"
   - "[Processor] [RAM] [Graphics] computer price"
   - "Similar specs [Type] marketplace price"

   For EACH search result you find, you MUST extract and include in the searchResults array:
   - The exact title of the listing
   - The URL to the listing
   - The price (exact amount or range like "$450-550")
   - The platform (eBay, Facebook Marketplace, etc.)
   - The condition mentioned (Used, Like New, Fair, etc.)
   - A brief description of what you found

   If you cannot find search results, you MUST indicate this clearly by returning an empty searchResults array.

   Consider factors like:
   - Age and generation of components (newer = higher value)
   - Performance tier (gaming vs office vs budget)
   - Market demand for specific components
   - Depreciation rates for tech products

   Based on your search results, suggest three pricing tiers in USD: 'Quick Sale', 'Fair Market', and 'Ambitious'. Each justification MUST cite specific search results with examples like: 'Based on 3 sold eBay listings for similar i7-12700H laptops ranging $650-750, and 2 current listings at $800-900, a fair market price is...'

Your response MUST be a single, valid JSON object that strictly adheres to the structure provided below. Do not include any text, markdown, or formatting like \\\`\\\`\\\`json before or after the JSON object.
${jsonStructure}`;

export async function analyzeComputer(specs: ComputerSpecs): Promise<ProductAnalysis> {
  const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

  // Create a detailed description from the specs
  const specsDescription = `
Computer Type: ${specs.type}
Brand: ${specs.brand}
Model: ${specs.model}
Processor: ${specs.processor}
RAM: ${specs.ram}
Storage: ${specs.storage}
Graphics: ${specs.graphics}
${specs.screenSize ? `Screen Size: ${specs.screenSize}` : ''}
Condition: ${specs.condition}
Additional Notes: ${specs.additionalNotes}
  `.trim();

  const textPart = {
    text: `Computer specifications: ${specsDescription}`,
  };

  let lastResponse: any;
  let sources: GroundingSource[] = [];

  // Attempt 1: Standard instruction
  try {
    const result = await attemptAnalysis(ai, [], textPart, computerSystemInstruction, 1);
    lastResponse = result.response;
    sources = result.sources;

    // Check if we got search results in the JSON response
    let hasSearchResults = false;
    try {
      const responseText = result.response.text;
      const jsonStart = responseText.indexOf('{');
      const jsonEnd = responseText.lastIndexOf('}');
      if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd >= jsonStart) {
        const jsonText = responseText.substring(jsonStart, jsonEnd + 1);
        const parsed = JSON.parse(jsonText);
        hasSearchResults = parsed.searchResults && parsed.searchResults.length > 0;
      }
    } catch (e) {
      // If parsing fails, we'll rely on grounding chunks
    }

    // If we got search results, proceed
    if (sources.length > 0 || hasSearchResults) {
      console.log(
        `Computer analysis success: Found ${sources.length} grounding sources and ${hasSearchResults ? 'JSON' : 'no'} search results on first attempt`
      );
    } else {
      console.warn('Computer analysis: No search results found, trying retry...');

      // Attempt 2: Retry with stronger instruction
      const retryInstruction = `You are a computer hardware expert. You FAILED to provide search results in your previous attempt. This is CRITICAL for computer pricing.

MANDATORY: Perform Google Search RIGHT NOW for the computer specifications provided. Search for:
- Used/sold listings on eBay, Facebook Marketplace, Craigslist
- Current market prices for similar specs
- Comparable computers with similar performance

For EACH search result you find, you MUST include it in the searchResults array with:
- Exact title, URL, price, platform, condition, and description

Do not respond without actual search results. If you truly cannot find any search results, return an empty searchResults array.

${jsonStructure}`;

      const retryResult = await attemptAnalysis(ai, [], textPart, retryInstruction, 2);
      lastResponse = retryResult.response;
      sources = retryResult.sources;

      console.log(`Computer analysis retry: Found ${sources.length} grounding sources`);
    }
  } catch (error) {
    console.error('Error during computer analysis:', error);
    throw error;
  }

  // Process the final response (same logic as identifyAndPriceProduct)
  const responseText = lastResponse.text;
  const jsonStart = responseText.indexOf('{');
  const jsonEnd = responseText.lastIndexOf('}');

  if (jsonStart === -1 || jsonEnd === -1 || jsonEnd < jsonStart) {
    console.error('Failed to find JSON object in computer analysis response:', responseText);
    throw new Error('AI returned an invalid response. Please try again.');
  }

  const jsonText = responseText.substring(jsonStart, jsonEnd + 1);

  try {
    const analysisPart: Omit<ProductAnalysis, 'sources'> = JSON.parse(jsonText);

    // Ensure searchResults exists, default to empty array if not provided
    const searchResults = analysisPart.searchResults || [];

    // Basic validation - just ensure we have required fields
    const validSearchResults = searchResults.filter(result => {
      const isValid = result.url && result.title && result.price;
      if (!isValid) {
        console.warn(`Filtered out incomplete computer search result: ${JSON.stringify(result)}`);
      }
      return isValid;
    });

    console.log(
      `Computer search results validation: ${searchResults.length} -> ${validSearchResults.length} valid results`
    );

    // If no search results were found, modify the pricing justifications
    if (sources.length === 0 && validSearchResults.length === 0) {
      console.warn(
        'No search results found for computer analysis - updating pricing justifications'
      );
      analysisPart.pricing = {
        quickSale: {
          price: 0,
          justification:
            'Unable to determine pricing - no comparable computer listings found in search results. Please research manually on tech marketplaces.',
        },
        fairMarket: {
          price: 0,
          justification:
            'Unable to determine pricing - no comparable computer listings found in search results. Please research manually on tech marketplaces.',
        },
        ambitious: {
          price: 0,
          justification:
            'Unable to determine pricing - no comparable computer listings found in search results. Please research manually on tech marketplaces.',
        },
      };
    }

    const result: ProductAnalysis = {
      ...analysisPart,
      sources: sources,
      searchResults: validSearchResults,
    };

    console.log(
      `Computer analysis final result: ${sources.length} sources, ${validSearchResults.length} valid search results, product: ${result.productName}`
    );
    return result;
  } catch (e) {
    console.error('Failed to parse JSON response from computer analysis:', jsonText);
    // Always return a fallback result so the UI can display an error
    return {
      productName: 'Unknown Computer',
      suggestedTitle: 'No title available',
      detailedDescription:
        'No description available. Computer analysis did not return valid results.',
      keywords: [],
      pricing: {
        quickSale: { price: 0, justification: 'No pricing available due to parsing error.' },
        fairMarket: { price: 0, justification: 'No pricing available due to parsing error.' },
        ambitious: { price: 0, justification: 'No pricing available due to parsing error.' },
      },
      sources: [],
      searchResults: [],
    };
  }
}

const specsLookupInstruction = `You are an expert computer hardware specialist with access to comprehensive technical databases. Your task is to find detailed technical specifications for a specific computer model.

CRITICAL REQUIREMENT: You MUST perform Google Search to find official specifications and all available configurations for the requested computer model.

Your task:
1. **Search for Official Specs**: Find the official technical specifications from the manufacturer's website, tech review sites, and reliable databases.
2. **Find All Variations**: Look for different configurations/models within the same product line (different RAM, storage, processor options).
3. **Gather Complete Details**: Get comprehensive specifications including processor, RAM options, storage options, graphics, display, ports, dimensions, weight, etc.
4. **Verify Information**: Cross-reference multiple sources to ensure accuracy.

Search for:
- Official manufacturer specifications
- Tech review sites (AnandTech, TechSpot, NotebookCheck, etc.)
- Retailer specifications (Best Buy, Amazon, Newegg)
- Database sites (TechSpecs, GSMArena for laptops)

For EACH configuration/variation you find, include:
- Variation name (e.g., "Base Model", "Gaming Edition", "Pro")
- Exact processor model
- All available RAM configurations
- All available storage options
- Graphics card options
- Screen specifications (for laptops)
- Any other distinguishing features

Your response MUST be a single, valid JSON object with this structure:
{
  "found": true,
  "brand": "Exact brand name",
  "model": "Exact model name",
  "type": "laptop" | "desktop" | "all-in-one",
  "release_year": 2023,
  "variations": [
    {
      "variation_name": "Base Model",
      "processor": "Intel Core i7-12700H",
      "ram_options": ["8GB DDR4", "16GB DDR4", "32GB DDR4"],
      "storage_options": ["256GB SSD", "512GB SSD", "1TB SSD"],
      "graphics_options": ["Intel Iris Xe", "NVIDIA RTX 3060"],
      "screen_size": "15.6 inch",
      "additional_specs": {
        "display_resolution": "1920x1080",
        "display_type": "IPS",
        "refresh_rate": "60Hz",
        "ports": ["USB-A", "USB-C", "HDMI", "Audio Jack"],
        "weight": "2.1 kg",
        "battery": "56Wh",
        "operating_system": "Windows 11"
      }
    }
  ]
}

If you cannot find the computer model, respond with:
{
  "found": false,
  "message": "Computer model not found or insufficient information available"
}`;

export async function lookupComputerSpecs(
  brand: string,
  model: string
): Promise<ComputerLookupResult> {
  const db = getDatabase();

  // First, check if we have this computer in our database
  console.log(`Looking up specs for: ${brand} ${model}`);
  const cachedComputer = await db.findComputer(brand, model);

  if (cachedComputer) {
    console.log(`Found cached specs for ${brand} ${model}`);
    return {
      found: true,
      computer: cachedComputer,
      source: 'database',
    };
  }

  // If not in database, use Gemini to search for specs
  console.log(`Searching online for specs: ${brand} ${model}`);

  try {
    const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

    const textPart = {
      text: `Find detailed technical specifications for: ${brand} ${model}`,
    };

    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: { parts: [textPart] },
      config: {
        systemInstruction: specsLookupInstruction,
        temperature: 0.1, // Lower temperature for more factual responses
        tools: [{ googleSearch: {} }],
      },
    });

    const responseText = response.text;
    const jsonStart = responseText.indexOf('{');
    const jsonEnd = responseText.lastIndexOf('}');

    if (jsonStart === -1 || jsonEnd === -1 || jsonEnd < jsonStart) {
      console.error('Failed to find JSON object in specs lookup response:', responseText);
      return {
        found: false,
        source: 'api',
      };
    }

    const jsonText = responseText.substring(jsonStart, jsonEnd + 1);
    const specsData = JSON.parse(jsonText);

    if (!specsData.found) {
      console.log(`Specs not found for ${brand} ${model}`);
      return {
        found: false,
        source: 'api',
      };
    }

    // Save to database
    const computerModel: Omit<ComputerModel, 'id' | 'created_at' | 'updated_at'> = {
      brand: specsData.brand,
      model: specsData.model,
      type: specsData.type,
      release_year: specsData.release_year,
      variations: specsData.variations,
    };

    const savedComputer = await db.saveComputer(computerModel);
    console.log(`Saved specs to database for ${brand} ${model}`);

    return {
      found: true,
      computer: savedComputer,
      source: 'api',
    };
  } catch (error) {
    console.error('Error during specs lookup:', error);
    return {
      found: false,
      source: 'api',
    };
  }
}

// Device lookup for various categories
const deviceLookupInstruction = `You are an expert electronics specialist with access to comprehensive technical databases. Your task is to find detailed specifications for any electronic device.

CRITICAL REQUIREMENT: You MUST perform Google Search to find official specifications and all available configurations for the requested device.

Your response MUST be a single, valid JSON object with this structure:
{
  "found": true,
  "category": "smartphone" | "tablet" | "gaming_console" | "audio_equipment" | "networking_hardware" | "camera" | "wearable" | "smart_home",
  "brand": "Exact brand name",
  "model": "Exact model name",
  "release_year": 2023,
  "discontinued": false,
  "msrp": 999,
  "specifications": {
    "key": "value pairs of all relevant specs"
  },
  "variations": [
    {
      "variation_name": "Base Model",
      "specifications": {
        "storage": "128GB",
        "color": "Black"
      },
      "price_difference": 0,
      "availability": "Available"
    }
  ]
}`;

export async function lookupDeviceSpecs(
  brand: string,
  model: string,
  category: DeviceCategory
): Promise<{ found: boolean; device?: Device; source: 'database' | 'api' }> {
  const db = getExpandedDatabase();

  console.log(`Looking up device specs for: ${brand} ${model} (${category})`);
  const cachedDevice = await db.findDevice(brand, model, category);

  if (cachedDevice) {
    console.log(`Found cached device specs for ${brand} ${model}`);
    return {
      found: true,
      device: cachedDevice,
      source: 'database',
    };
  }

  console.log(`Searching online for device specs: ${brand} ${model} (${category})`);

  try {
    const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

    const textPart = {
      text: `Find detailed specifications for this ${category}: ${brand} ${model}`,
    };

    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: { parts: [textPart] },
      config: {
        systemInstruction: deviceLookupInstruction,
        temperature: 0.1,
        tools: [{ googleSearch: {} }],
      },
    });

    const responseText = response.text;
    const jsonStart = responseText.indexOf('{');
    const jsonEnd = responseText.lastIndexOf('}');

    if (jsonStart === -1 || jsonEnd === -1 || jsonEnd < jsonStart) {
      console.error('Failed to find JSON object in device lookup response:', responseText);
      return { found: false, source: 'api' };
    }

    const jsonText = responseText.substring(jsonStart, jsonEnd + 1);
    const deviceData = JSON.parse(jsonText);

    if (!deviceData.found) {
      console.log(`Device specs not found for ${brand} ${model}`);
      return { found: false, source: 'api' };
    }

    const device: Omit<Device, 'id' | 'created_at' | 'updated_at'> = {
      category: deviceData.category,
      brand: deviceData.brand,
      model: deviceData.model,
      release_year: deviceData.release_year,
      discontinued: deviceData.discontinued,
      msrp: deviceData.msrp,
      specifications: deviceData.specifications,
      variations: deviceData.variations,
    };

    const savedDevice = await db.saveDevice(device);
    console.log(`Saved device specs to database for ${brand} ${model}`);

    return {
      found: true,
      device: savedDevice,
      source: 'api',
    };
  } catch (error) {
    console.error('Error during device lookup:', error);
    return { found: false, source: 'api' };
  }
}

// Component lookup
const componentLookupInstruction = `You are an expert computer hardware specialist. Your task is to find detailed specifications for computer components.

CRITICAL REQUIREMENT: You MUST perform Google Search to find official specifications, performance benchmarks, and compatibility information.

Your response MUST be a single, valid JSON object:
{
  "found": true,
  "type": "gpu" | "cpu" | "ram" | "storage" | "motherboard" | "psu" | "cooling" | "case" | "monitor" | "keyboard" | "mouse" | "headset" | "speaker" | "webcam",
  "brand": "Exact brand name",
  "model": "Exact model name",
  "series": "Product series if applicable",
  "release_year": 2023,
  "discontinued": false,
  "msrp": 299,
  "specifications": {
    "detailed": "component specifications"
  },
  "performance_metrics": {
    "benchmarks": "and performance data"
  },
  "compatibility": ["compatible", "systems", "or", "standards"]
}`;

export async function lookupComponentSpecs(
  brand: string,
  model: string,
  type: ComponentType
): Promise<{ found: boolean; component?: Component; source: 'database' | 'api' }> {
  const db = getExpandedDatabase();

  console.log(`Looking up component specs for: ${brand} ${model} (${type})`);
  const cachedComponent = await db.findComponent(brand, model, type);

  if (cachedComponent) {
    console.log(`Found cached component specs for ${brand} ${model}`);
    return {
      found: true,
      component: cachedComponent,
      source: 'database',
    };
  }

  console.log(`Searching online for component specs: ${brand} ${model} (${type})`);

  try {
    const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

    const textPart = {
      text: `Find detailed specifications and performance data for this ${type}: ${brand} ${model}`,
    };

    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: { parts: [textPart] },
      config: {
        systemInstruction: componentLookupInstruction,
        temperature: 0.1,
        tools: [{ googleSearch: {} }],
      },
    });

    const responseText = response.text;
    const jsonStart = responseText.indexOf('{');
    const jsonEnd = responseText.lastIndexOf('}');

    if (jsonStart === -1 || jsonEnd === -1 || jsonEnd < jsonStart) {
      console.error('Failed to find JSON object in component lookup response:', responseText);
      return { found: false, source: 'api' };
    }

    const jsonText = responseText.substring(jsonStart, jsonEnd + 1);
    const componentData = JSON.parse(jsonText);

    if (!componentData.found) {
      console.log(`Component specs not found for ${brand} ${model}`);
      return { found: false, source: 'api' };
    }

    const component: Omit<Component, 'id' | 'created_at' | 'updated_at'> = {
      type: componentData.type,
      brand: componentData.brand,
      model: componentData.model,
      series: componentData.series,
      release_year: componentData.release_year,
      discontinued: componentData.discontinued,
      msrp: componentData.msrp,
      specifications: componentData.specifications,
      performance_metrics: componentData.performance_metrics,
      compatibility: componentData.compatibility,
    };

    const savedComponent = await db.saveComponent(component);
    console.log(`Saved component specs to database for ${brand} ${model}`);

    return {
      found: true,
      component: savedComponent,
      source: 'api',
    };
  } catch (error) {
    console.error('Error during component lookup:', error);
    return { found: false, source: 'api' };
  }
}
