import React, { useState } from 'react';
import { Device<PERSON>ategory, Device } from '../types.ts';
import { MagnifyingGlassIcon, DevicePhoneMobileIcon, CheckIcon } from './icons.tsx';

interface DeviceSearchProps {
  onDeviceFound: (device: Device) => void;
}

const deviceCategories: Array<{value: DeviceCategory, label: string}> = [
  { value: 'smartphone', label: 'Smartphone' },
  { value: 'tablet', label: 'Tablet' },
  { value: 'gaming_console', label: 'Gaming Console' },
  { value: 'audio_equipment', label: 'Audio Equipment' },
  { value: 'networking_hardware', label: 'Networking Hardware' },
  { value: 'camera', label: 'Camera' },
  { value: 'wearable', label: 'Wearable Device' },
  { value: 'smart_home', label: 'Smart Home Device' }
];

export const DeviceSearch: React.FC<DeviceSearchProps> = ({ onDeviceFound }) => {
  const [category, setCategory] = useState<DeviceCategory>('smartphone');
  const [brand, setBrand] = useState('');
  const [model, setModel] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResult, setSearchResult] = useState<Device | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [searchSource, setSearchSource] = useState<'database' | 'api' | null>(null);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!brand.trim() || !model.trim()) {
      setError('Please enter both brand and model.');
      return;
    }

    setIsSearching(true);
    setError(null);
    setSearchResult(null);

    try {
      const { lookupDeviceSpecs } = await import('../services/geminiService.ts');
      const result = await lookupDeviceSpecs(brand.trim(), model.trim(), category);
      
      if (result.found && result.device) {
        setSearchResult(result.device);
        setSearchSource(result.source);
      } else {
        setError('Device not found. Please check the brand, model, and category.');
      }
    } catch (err) {
      console.error('Device lookup error:', err);
      setError('An error occurred while searching for device specifications. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const handleUseDevice = () => {
    if (searchResult) {
      onDeviceFound(searchResult);
    }
  };

  const formatSpecifications = (specs: Record<string, any>) => {
    return Object.entries(specs).map(([key, value]) => (
      <div key={key} className="flex justify-between text-sm py-1">
        <span className="text-slate-600 dark:text-slate-400 capitalize">
          {key.replace(/_/g, ' ')}:
        </span>
        <span className="text-slate-800 dark:text-slate-200 font-medium">
          {Array.isArray(value) ? value.join(', ') : String(value)}
        </span>
      </div>
    ));
  };

  return (
    <div className="bg-white dark:bg-slate-800 p-6 sm:p-8 rounded-2xl shadow-lg">
      <div className="text-center mb-6">
        <DevicePhoneMobileIcon className="w-12 h-12 text-primary-500 mx-auto mb-3" />
        <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-100">Device Lookup</h2>
        <p className="text-slate-600 dark:text-slate-300 mt-2">
          Find detailed specifications for electronic devices
        </p>
      </div>

      {/* Search Form */}
      <form onSubmit={handleSearch} className="space-y-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Device Category *
          </label>
          <select
            value={category}
            onChange={(e) => setCategory(e.target.value as DeviceCategory)}
            className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
            disabled={isSearching}
          >
            {deviceCategories.map(cat => (
              <option key={cat.value} value={cat.value}>{cat.label}</option>
            ))}
          </select>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Brand *
            </label>
            <input
              type="text"
              value={brand}
              onChange={(e) => setBrand(e.target.value)}
              placeholder="e.g., Apple, Samsung, Sony, Nintendo"
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
              disabled={isSearching}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Model *
            </label>
            <input
              type="text"
              value={model}
              onChange={(e) => setModel(e.target.value)}
              placeholder="e.g., iPhone 15 Pro, Galaxy S24, PS5"
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
              disabled={isSearching}
            />
          </div>
        </div>

        {error && <p className="text-sm text-red-500 dark:text-red-400 text-center">{error}</p>}
        
        <div className="pt-2">
          <button
            type="submit"
            disabled={isSearching || !brand.trim() || !model.trim()}
            className="w-full flex items-center justify-center gap-2 bg-primary-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-4 focus:ring-primary-500/50 transition-all disabled:bg-slate-400 dark:disabled:bg-slate-600 disabled:cursor-not-allowed"
          >
            <MagnifyingGlassIcon className="w-5 h-5" />
            {isSearching ? 'Searching...' : 'Find Device'}
          </button>
        </div>
      </form>

      {/* Search Results */}
      {searchResult && (
        <div className="border-t border-slate-200 dark:border-slate-700 pt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
              {searchResult.brand} {searchResult.model}
            </h3>
            <div className="flex items-center gap-2">
              <span className={`text-xs px-2 py-1 rounded-full ${
                searchSource === 'database' 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                  : 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300'
              }`}>
                {searchSource === 'database' ? 'Cached' : 'Live Search'}
              </span>
              <span className="text-xs text-slate-500 dark:text-slate-400 capitalize">
                {searchResult.category.replace('_', ' ')}
              </span>
              {searchResult.release_year && (
                <span className="text-xs text-slate-500 dark:text-slate-400">
                  {searchResult.release_year}
                </span>
              )}
            </div>
          </div>

          <div className="bg-slate-50 dark:bg-slate-700/50 p-4 rounded-lg">
            {/* Specifications */}
            <div className="mb-4">
              <h4 className="font-medium text-slate-700 dark:text-slate-300 mb-3">
                Specifications
              </h4>
              <div className="space-y-1">
                {formatSpecifications(searchResult.specifications)}
              </div>
            </div>

            {/* Variations */}
            {searchResult.variations && searchResult.variations.length > 0 && (
              <div className="mt-4 pt-4 border-t border-slate-200 dark:border-slate-600">
                <h4 className="font-medium text-slate-700 dark:text-slate-300 mb-3">
                  Available Variations ({searchResult.variations.length})
                </h4>
                <div className="space-y-2">
                  {searchResult.variations.map((variation, index) => (
                    <div key={index} className="bg-white dark:bg-slate-600 p-3 rounded">
                      <h5 className="font-medium text-slate-800 dark:text-slate-100 mb-2">
                        {variation.variation_name}
                      </h5>
                      <div className="space-y-1">
                        {formatSpecifications(variation.specifications)}
                      </div>
                      {variation.price_difference !== undefined && variation.price_difference !== 0 && (
                        <div className="mt-2 text-sm">
                          <span className="text-slate-600 dark:text-slate-400">Price difference: </span>
                          <span className={`font-medium ${
                            variation.price_difference > 0 
                              ? 'text-red-600 dark:text-red-400' 
                              : 'text-green-600 dark:text-green-400'
                          }`}>
                            {variation.price_difference > 0 ? '+' : ''}${variation.price_difference}
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Pricing */}
            {searchResult.msrp && (
              <div className="mt-4 pt-4 border-t border-slate-200 dark:border-slate-600">
                <div className="flex justify-between items-center">
                  <span className="text-slate-600 dark:text-slate-400">MSRP:</span>
                  <span className="font-bold text-green-600 dark:text-green-400 text-lg">
                    ${searchResult.msrp}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Use Device Button */}
          <div className="mt-6 pt-4 border-t border-slate-200 dark:border-slate-700">
            <button
              onClick={handleUseDevice}
              className="w-full bg-green-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-4 focus:ring-green-500/50 transition-all flex items-center justify-center gap-2"
            >
              <CheckIcon className="w-5 h-5" />
              Add to Database
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
