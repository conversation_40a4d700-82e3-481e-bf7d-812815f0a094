import React, { useState } from 'react';
import { ComputerSpecs } from '../types.ts';
import { MagnifyingGlassIcon, ComputerDesktopIcon } from './icons.tsx';

interface ComputerFormProps {
  onAnalyze: (specs: ComputerSpecs) => void;
}

export const ComputerForm: React.FC<ComputerFormProps> = ({ onAnalyze }) => {
  const [specs, setSpecs] = useState<ComputerSpecs>({
    type: 'laptop',
    brand: '',
    model: '',
    processor: '',
    ram: '',
    storage: '',
    graphics: '',
    screenSize: '',
    condition: '',
    additionalNotes: ''
  });
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!specs.brand.trim()) {
      setError('Please enter the brand.');
      return;
    }
    if (!specs.model.trim()) {
      setError('Please enter the model.');
      return;
    }
    if (!specs.processor.trim()) {
      setError('Please enter the processor information.');
      return;
    }
    if (!specs.condition.trim()) {
      setError('Please select the condition.');
      return;
    }
    
    setError(null);
    onAnalyze(specs);
  };

  const handleInputChange = (field: keyof ComputerSpecs, value: string) => {
    setSpecs(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="bg-white dark:bg-slate-800 p-6 sm:p-8 rounded-2xl shadow-lg">
      <div className="text-center mb-6">
        <ComputerDesktopIcon className="w-12 h-12 text-primary-500 mx-auto mb-3" />
        <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-100">Computer Specifications</h2>
        <p className="text-slate-600 dark:text-slate-300 mt-2">
          Enter your computer's specifications to get accurate pricing analysis
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Computer Type */}
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Computer Type *
          </label>
          <select
            value={specs.type}
            onChange={(e) => handleInputChange('type', e.target.value as 'laptop' | 'desktop' | 'all-in-one')}
            className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
          >
            <option value="laptop">Laptop</option>
            <option value="desktop">Desktop</option>
            <option value="all-in-one">All-in-One</option>
          </select>
        </div>

        {/* Brand and Model */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Brand *
            </label>
            <input
              type="text"
              value={specs.brand}
              onChange={(e) => handleInputChange('brand', e.target.value)}
              placeholder="e.g., Dell, HP, Apple, Lenovo"
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Model *
            </label>
            <input
              type="text"
              value={specs.model}
              onChange={(e) => handleInputChange('model', e.target.value)}
              placeholder="e.g., XPS 13, ThinkPad X1, MacBook Pro"
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
            />
          </div>
        </div>

        {/* Processor */}
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Processor *
          </label>
          <input
            type="text"
            value={specs.processor}
            onChange={(e) => handleInputChange('processor', e.target.value)}
            placeholder="e.g., Intel i7-12700H, AMD Ryzen 5 5600X, Apple M2"
            className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
          />
        </div>

        {/* RAM and Storage */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              RAM
            </label>
            <input
              type="text"
              value={specs.ram}
              onChange={(e) => handleInputChange('ram', e.target.value)}
              placeholder="e.g., 16GB DDR4, 32GB"
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Storage
            </label>
            <input
              type="text"
              value={specs.storage}
              onChange={(e) => handleInputChange('storage', e.target.value)}
              placeholder="e.g., 512GB SSD, 1TB NVMe"
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
            />
          </div>
        </div>

        {/* Graphics */}
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Graphics
          </label>
          <input
            type="text"
            value={specs.graphics}
            onChange={(e) => handleInputChange('graphics', e.target.value)}
            placeholder="e.g., Integrated, RTX 3060, GTX 1660 Ti"
            className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
          />
        </div>

        {/* Screen Size (for laptops) */}
        {specs.type === 'laptop' && (
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Screen Size
            </label>
            <input
              type="text"
              value={specs.screenSize}
              onChange={(e) => handleInputChange('screenSize', e.target.value)}
              placeholder="e.g., 13.3 inch, 15.6 inch"
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
            />
          </div>
        )}

        {/* Condition */}
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Condition *
          </label>
          <select
            value={specs.condition}
            onChange={(e) => handleInputChange('condition', e.target.value)}
            className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
          >
            <option value="">Select condition</option>
            <option value="Like New">Like New</option>
            <option value="Excellent">Excellent</option>
            <option value="Good">Good</option>
            <option value="Fair">Fair</option>
            <option value="Poor">Poor</option>
            <option value="For Parts">For Parts</option>
          </select>
        </div>

        {/* Additional Notes */}
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Additional Notes
          </label>
          <textarea
            value={specs.additionalNotes}
            onChange={(e) => handleInputChange('additionalNotes', e.target.value)}
            placeholder="Any additional details, issues, or special features..."
            rows={3}
            className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
          />
        </div>

        {error && <p className="text-sm text-red-500 dark:text-red-400 text-center">{error}</p>}
        
        <div className="pt-4">
          <button
            type="submit"
            className="w-full flex items-center justify-center gap-2 bg-primary-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-4 focus:ring-primary-500/50 transition-all"
          >
            <MagnifyingGlassIcon className="w-5 h-5" />
            Analyze Computer
          </button>
        </div>
      </form>
    </div>
  );
};
