import React, { useState, useEffect } from 'react';
import { <PERSON>ce, Component, SearchFilter, DatabaseStats, DeviceCategory, ComponentType } from '../types.ts';
import { MagnifyingGlassIcon, FunnelIcon, ChartBarIcon, DatabaseIcon, ArrowDownTrayIcon, ArrowUpTrayIcon } from './icons.tsx';
import { ItemDetailView } from './ItemDetailView.tsx';

interface DatabaseBrowserProps {
  onItemSelect?: (item: Device | Component) => void;
}

export const DatabaseBrowser: React.FC<DatabaseBrowserProps> = ({ onItemSelect }) => {

export const DatabaseBrowser: React.FC<DatabaseBrowserProps> = ({ onItemSelect }) => {
  const [activeTab, setActiveTab] = useState<'devices' | 'components' | 'stats'>('devices');
  const [devices, setDevices] = useState<Device[]>([]);
  const [components, setComponents] = useState<Component[]>([]);
  const [stats, setStats] = useState<DatabaseStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [searchFilter, setSearchFilter] = useState<SearchFilter>({});
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState<DeviceCategory[]>([]);
  const [selectedComponentTypes, setSelectedComponentTypes] = useState<ComponentType[]>([]);
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 10000]);
  const [yearRange, setYearRange] = useState<[number, number]>([2000, new Date().getFullYear()]);
  const [availableBrands, setAvailableBrands] = useState<string[]>([]);
  const [selectedItem, setSelectedItem] = useState<Device | Component | null>(null);
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [itemsPerPage] = useState(20);
  const [totalItems, setTotalItems] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    setCurrentPage(0); // Reset page when tab or filter changes
    loadData();
  }, [activeTab, searchFilter]);

  useEffect(() => {
    loadData();
  }, [currentPage]);

  useEffect(() => {
    loadAvailableBrands();
  }, [activeTab]);

  useEffect(() => {
    // Update search filter when filter controls change
    const newFilter: SearchFilter = {
      text_search: searchFilter.text_search,
      category: selectedCategories.length > 0 ? selectedCategories : undefined,
      component_type: selectedComponentTypes.length > 0 ? selectedComponentTypes : undefined,
      brand: selectedBrands.length > 0 ? selectedBrands : undefined,
      price_range: priceRange[0] > 0 || priceRange[1] < 10000 ? priceRange : undefined,
      release_year_range: yearRange[0] > 2000 || yearRange[1] < new Date().getFullYear() ? yearRange : undefined
    };
    setSearchFilter(newFilter);
  }, [selectedCategories, selectedComponentTypes, selectedBrands, priceRange, yearRange]);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const { getExpandedDatabase } = await import('../services/expandedDatabaseService.ts');
      const db = getExpandedDatabase();

      const offset = currentPage * itemsPerPage;

      if (activeTab === 'devices') {
        const deviceResults = await db.searchDevices(searchFilter, itemsPerPage, offset);

        if (currentPage === 0) {
          setDevices(deviceResults);
        } else {
          // Append to existing results for infinite scroll
          setDevices(prev => [...prev, ...deviceResults]);
        }

        setHasMore(deviceResults.length === itemsPerPage);
        setTotalItems(prev => currentPage === 0 ? deviceResults.length : prev + deviceResults.length);

      } else if (activeTab === 'components') {
        const componentResults = await db.searchComponents(searchFilter, itemsPerPage, offset);

        if (currentPage === 0) {
          setComponents(componentResults);
        } else {
          // Append to existing results for infinite scroll
          setComponents(prev => [...prev, ...componentResults]);
        }

        setHasMore(componentResults.length === itemsPerPage);
        setTotalItems(prev => currentPage === 0 ? componentResults.length : prev + componentResults.length);

      } else if (activeTab === 'stats') {
        const statsData = await db.getDatabaseStats();
        setStats(statsData);
      }
    } catch (error) {
      console.error('Error loading database data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadAvailableBrands = async () => {
    try {
      const { getExpandedDatabase } = await import('../services/expandedDatabaseService.ts');
      const db = getExpandedDatabase();

      // Get all brands from both devices and components
      const allDevices = await db.searchDevices({}, 1000, 0);
      const allComponents = await db.searchComponents({}, 1000, 0);

      const deviceBrands = allDevices.map(d => d.brand);
      const componentBrands = allComponents.map(c => c.brand);
      const uniqueBrands = Array.from(new Set([...deviceBrands, ...componentBrands])).sort();

      setAvailableBrands(uniqueBrands);
    } catch (error) {
      console.error('Error loading brands:', error);
    }
  };

  const handleTextSearch = (text: string) => {
    setSearchFilter(prev => ({ ...prev, text_search: text || undefined }));
  };

  const handleCategoryFilter = (categories: DeviceCategory[]) => {
    setSearchFilter(prev => ({ ...prev, category: categories.length > 0 ? categories : undefined }));
  };

  const handleComponentTypeFilter = (types: ComponentType[]) => {
    setSearchFilter(prev => ({ ...prev, component_type: types.length > 0 ? types : undefined }));
  };

  const handleExport = async (type: 'all' | 'devices' | 'components' | 'csv') => {
    setIsExporting(true);
    try {
      const { getExportImportService } = await import('../services/exportImportService.ts');
      const exportService = getExportImportService();

      let content: string;
      let filename: string;
      let contentType = 'application/json';

      switch (type) {
        case 'all':
          content = await exportService.exportAllData();
          filename = `electronics_database_${new Date().toISOString().split('T')[0]}.json`;
          break;
        case 'devices':
          content = await exportService.exportDevices();
          filename = `devices_${new Date().toISOString().split('T')[0]}.json`;
          break;
        case 'components':
          content = await exportService.exportComponents();
          filename = `components_${new Date().toISOString().split('T')[0]}.json`;
          break;
        case 'csv':
          content = await exportService.exportDevicesCSV();
          filename = `devices_${new Date().toISOString().split('T')[0]}.csv`;
          contentType = 'text/csv';
          break;
      }

      exportService.downloadFile(content, filename, contentType);
      setShowExportMenu(false);
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);
    try {
      const content = await file.text();
      const { getExportImportService } = await import('../services/exportImportService.ts');
      const exportService = getExportImportService();

      const validation = exportService.validateImportFile(content);
      if (!validation.valid) {
        alert(`Import validation failed:\n${validation.errors.join('\n')}`);
        return;
      }

      const result = await exportService.importData(content);

      if (result.success) {
        alert(`Import successful! Imported ${result.imported} items.`);
        loadData(); // Refresh the data
      } else {
        alert(`Import failed:\n${result.errors.join('\n')}`);
      }
    } catch (error) {
      console.error('Import failed:', error);
      alert('Import failed. Please check the file format and try again.');
    } finally {
      setIsImporting(false);
      // Reset the file input
      event.target.value = '';
    }
  };

  const loadMore = () => {
    if (!isLoading && hasMore) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const formatSpecifications = (specs: Record<string, any>) => {
    const entries = Object.entries(specs).slice(0, 3); // Show first 3 specs
    return entries.map(([key, value]) => (
      <span key={key} className="text-xs bg-slate-100 dark:bg-slate-600 text-slate-700 dark:text-slate-300 px-2 py-1 rounded mr-1 mb-1 inline-block">
        {key.replace(/_/g, ' ')}: {Array.isArray(value) ? value[0] : String(value).slice(0, 20)}
      </span>
    ));
  };

  return (
    <div className="bg-white dark:bg-slate-800 p-6 sm:p-8 rounded-2xl shadow-lg">
      <div className="text-center mb-6">
        <DatabaseIcon className="w-12 h-12 text-primary-500 mx-auto mb-3" />
        <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-100">Electronics Database</h2>
        <p className="text-slate-600 dark:text-slate-300 mt-2">
          Browse and search all stored devices and components
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-slate-200 dark:border-slate-700 mb-6">
        <button
          onClick={() => setActiveTab('devices')}
          className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors ${
            activeTab === 'devices'
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-300'
          }`}
        >
          Devices
        </button>
        <button
          onClick={() => setActiveTab('components')}
          className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors ${
            activeTab === 'components'
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-300'
          }`}
        >
          Components
        </button>
        <button
          onClick={() => setActiveTab('stats')}
          className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors ${
            activeTab === 'stats'
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-300'
          }`}
        >
          Statistics
        </button>
      </div>

      {/* Search and Filters */}
      {(activeTab === 'devices' || activeTab === 'components') && (
        <div className="mb-6">
          <div className="flex gap-2 mb-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search devices and components..."
                onChange={(e) => handleTextSearch(e.target.value)}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
              />
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors"
            >
              <FunnelIcon className="w-5 h-5" />
            </button>

            {/* Export Menu */}
            <div className="relative">
              <button
                onClick={() => setShowExportMenu(!showExportMenu)}
                disabled={isExporting}
                className="px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors disabled:opacity-50"
              >
                <ArrowDownTrayIcon className="w-5 h-5" />
              </button>

              {showExportMenu && (
                <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg shadow-lg z-10">
                  <div className="p-2">
                    <button
                      onClick={() => handleExport('all')}
                      className="w-full text-left px-3 py-2 text-sm hover:bg-slate-100 dark:hover:bg-slate-600 rounded"
                    >
                      Export All Data (JSON)
                    </button>
                    <button
                      onClick={() => handleExport('devices')}
                      className="w-full text-left px-3 py-2 text-sm hover:bg-slate-100 dark:hover:bg-slate-600 rounded"
                    >
                      Export Devices (JSON)
                    </button>
                    <button
                      onClick={() => handleExport('components')}
                      className="w-full text-left px-3 py-2 text-sm hover:bg-slate-100 dark:hover:bg-slate-600 rounded"
                    >
                      Export Components (JSON)
                    </button>
                    <button
                      onClick={() => handleExport('csv')}
                      className="w-full text-left px-3 py-2 text-sm hover:bg-slate-100 dark:hover:bg-slate-600 rounded"
                    >
                      Export Devices (CSV)
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Import Button */}
            <label className="px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors cursor-pointer">
              <ArrowUpTrayIcon className="w-5 h-5" />
              <input
                type="file"
                accept=".json"
                onChange={handleImport}
                disabled={isImporting}
                className="hidden"
              />
            </label>
          </div>

          {/* Filter Panel */}
          {showFilters && (
            <div className="bg-slate-50 dark:bg-slate-700/50 p-4 rounded-lg mb-4">
              <h4 className="font-medium text-slate-700 dark:text-slate-300 mb-4">Advanced Filters</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Category Filter (for devices) */}
                {activeTab === 'devices' && (
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                      Device Categories
                    </label>
                    <div className="space-y-1 max-h-32 overflow-y-auto">
                      {['computer', 'smartphone', 'tablet', 'gaming_console', 'audio_equipment', 'networking_hardware', 'camera', 'wearable', 'smart_home'].map(category => (
                        <label key={category} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={selectedCategories.includes(category as DeviceCategory)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedCategories([...selectedCategories, category as DeviceCategory]);
                              } else {
                                setSelectedCategories(selectedCategories.filter(c => c !== category));
                              }
                            }}
                            className="mr-2"
                          />
                          <span className="text-sm text-slate-600 dark:text-slate-400 capitalize">
                            {category.replace('_', ' ')}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                )}

                {/* Component Type Filter (for components) */}
                {activeTab === 'components' && (
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                      Component Types
                    </label>
                    <div className="space-y-1 max-h-32 overflow-y-auto">
                      {['cpu', 'gpu', 'ram', 'storage', 'motherboard', 'psu', 'cooling', 'case', 'monitor', 'keyboard', 'mouse', 'headset', 'speaker', 'webcam'].map(type => (
                        <label key={type} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={selectedComponentTypes.includes(type as ComponentType)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedComponentTypes([...selectedComponentTypes, type as ComponentType]);
                              } else {
                                setSelectedComponentTypes(selectedComponentTypes.filter(t => t !== type));
                              }
                            }}
                            className="mr-2"
                          />
                          <span className="text-sm text-slate-600 dark:text-slate-400 uppercase">
                            {type}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                )}

                {/* Brand Filter */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Brands
                  </label>
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {availableBrands.slice(0, 10).map(brand => (
                      <label key={brand} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={selectedBrands.includes(brand)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedBrands([...selectedBrands, brand]);
                            } else {
                              setSelectedBrands(selectedBrands.filter(b => b !== brand));
                            }
                          }}
                          className="mr-2"
                        />
                        <span className="text-sm text-slate-600 dark:text-slate-400">
                          {brand}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Price Range */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Price Range ($)
                  </label>
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <input
                        type="number"
                        placeholder="Min"
                        value={priceRange[0]}
                        onChange={(e) => setPriceRange([parseInt(e.target.value) || 0, priceRange[1]])}
                        className="w-full px-2 py-1 text-sm border border-slate-300 dark:border-slate-600 rounded dark:bg-slate-700 dark:text-slate-100"
                      />
                      <input
                        type="number"
                        placeholder="Max"
                        value={priceRange[1]}
                        onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value) || 10000])}
                        className="w-full px-2 py-1 text-sm border border-slate-300 dark:border-slate-600 rounded dark:bg-slate-700 dark:text-slate-100"
                      />
                    </div>
                  </div>
                </div>

                {/* Year Range */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Release Year
                  </label>
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <input
                        type="number"
                        placeholder="From"
                        value={yearRange[0]}
                        onChange={(e) => setYearRange([parseInt(e.target.value) || 2000, yearRange[1]])}
                        className="w-full px-2 py-1 text-sm border border-slate-300 dark:border-slate-600 rounded dark:bg-slate-700 dark:text-slate-100"
                      />
                      <input
                        type="number"
                        placeholder="To"
                        value={yearRange[1]}
                        onChange={(e) => setYearRange([yearRange[0], parseInt(e.target.value) || new Date().getFullYear()])}
                        className="w-full px-2 py-1 text-sm border border-slate-300 dark:border-slate-600 rounded dark:bg-slate-700 dark:text-slate-100"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Clear Filters Button */}
              <div className="mt-4 pt-4 border-t border-slate-200 dark:border-slate-600">
                <button
                  onClick={() => {
                    setSelectedCategories([]);
                    setSelectedComponentTypes([]);
                    setSelectedBrands([]);
                    setPriceRange([0, 10000]);
                    setYearRange([2000, new Date().getFullYear()]);
                  }}
                  className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
                >
                  Clear All Filters
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Content */}
      {isLoading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
          <p className="text-slate-500 dark:text-slate-400 mt-2">Loading...</p>
        </div>
      ) : (
        <>
          {/* Devices Tab */}
          {activeTab === 'devices' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
                  Devices ({devices.length}{hasMore ? '+' : ''})
                </h3>
                <div className="text-sm text-slate-500 dark:text-slate-400">
                  Page {currentPage + 1} • {itemsPerPage} per page
                </div>
              </div>
              
              {devices.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-slate-500 dark:text-slate-400">No devices found</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {devices.map((device) => (
                    <div
                      key={device.id}
                      className="border border-slate-200 dark:border-slate-600 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => {
                        setSelectedItem(device);
                        onItemSelect?.(device);
                      }}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium text-slate-800 dark:text-slate-100">
                          {device.brand} {device.model}
                        </h4>
                        <span className="text-xs bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-300 px-2 py-1 rounded">
                          {device.category.replace('_', ' ')}
                        </span>
                      </div>
                      
                      <div className="mb-3">
                        {formatSpecifications(device.specifications)}
                      </div>
                      
                      <div className="flex justify-between items-center text-sm text-slate-500 dark:text-slate-400">
                        <span>{device.release_year || 'Unknown year'}</span>
                        {device.msrp && <span>${device.msrp}</span>}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Load More Button */}
              {hasMore && !isLoading && (
                <div className="mt-6 text-center">
                  <button
                    onClick={loadMore}
                    className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                  >
                    Load More Devices
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Components Tab */}
          {activeTab === 'components' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
                  Components ({components.length}{hasMore ? '+' : ''})
                </h3>
                <div className="text-sm text-slate-500 dark:text-slate-400">
                  Page {currentPage + 1} • {itemsPerPage} per page
                </div>
              </div>
              
              {components.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-slate-500 dark:text-slate-400">No components found</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {components.map((component) => (
                    <div
                      key={component.id}
                      className="border border-slate-200 dark:border-slate-600 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => {
                        setSelectedItem(component);
                        onItemSelect?.(component);
                      }}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium text-slate-800 dark:text-slate-100">
                          {component.brand} {component.model}
                        </h4>
                        <span className="text-xs bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300 px-2 py-1 rounded">
                          {component.type.toUpperCase()}
                        </span>
                      </div>
                      
                      <div className="mb-3">
                        {formatSpecifications(component.specifications)}
                      </div>
                      
                      <div className="flex justify-between items-center text-sm text-slate-500 dark:text-slate-400">
                        <span>{component.release_year || 'Unknown year'}</span>
                        {component.msrp && <span>${component.msrp}</span>}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Load More Button */}
              {hasMore && !isLoading && (
                <div className="mt-6 text-center">
                  <button
                    onClick={loadMore}
                    className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                  >
                    Load More Components
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Statistics Tab */}
          {activeTab === 'stats' && stats && (
            <div>
              <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100 mb-4">
                Database Statistics
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div className="bg-slate-50 dark:bg-slate-700/50 p-4 rounded-lg">
                  <h4 className="font-medium text-slate-700 dark:text-slate-300">Total Devices</h4>
                  <p className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {stats.total_devices}
                  </p>
                </div>
                
                <div className="bg-slate-50 dark:bg-slate-700/50 p-4 rounded-lg">
                  <h4 className="font-medium text-slate-700 dark:text-slate-300">Total Components</h4>
                  <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {stats.total_components}
                  </p>
                </div>
                
                <div className="bg-slate-50 dark:bg-slate-700/50 p-4 rounded-lg">
                  <h4 className="font-medium text-slate-700 dark:text-slate-300">Recent Additions</h4>
                  <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {stats.recent_additions}
                  </p>
                </div>
                
                <div className="bg-slate-50 dark:bg-slate-700/50 p-4 rounded-lg">
                  <h4 className="font-medium text-slate-700 dark:text-slate-300">Total Items</h4>
                  <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                    {stats.total_devices + stats.total_components}
                  </p>
                </div>
              </div>

              {/* Popular Searches */}
              {stats.popular_searches.length > 0 && (
                <div className="bg-slate-50 dark:bg-slate-700/50 p-4 rounded-lg">
                  <h4 className="font-medium text-slate-700 dark:text-slate-300 mb-3">
                    Popular Searches
                  </h4>
                  <div className="space-y-2">
                    {stats.popular_searches.slice(0, 5).map((search, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <span className="text-slate-600 dark:text-slate-400">{search.query}</span>
                        <span className="text-sm font-medium text-slate-800 dark:text-slate-200">
                          {search.count} searches
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </>
      )}
    </div>
    </div>
  );
};

  // Show item detail view if an item is selected
  if (selectedItem) {
    return <ItemDetailView item={selectedItem} onBack={() => setSelectedItem(null)} />;
  }

  return (
