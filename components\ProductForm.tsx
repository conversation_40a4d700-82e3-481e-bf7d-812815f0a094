import React, { useState } from 'react';
import { ImageUploader } from './ImageUploader.tsx';
import { MagnifyingGlassIcon } from './icons.tsx';

interface ProductFormProps {
  onAnalyze: (images: string[], description: string) => void;
}

export const ProductForm: React.FC<ProductFormProps> = ({ onAnalyze }) => {
  const [imageBases64, setImageBases64] = useState<string[]>([]);
  const [description, setDescription] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (imageBases64.length === 0) {
      setError('Please upload at least one image of the product.');
      return;
    }
    if (!description.trim()) {
      setError('Please provide a brief description or any known details.');
      return;
    }
    setError(null);
    onAnalyze(imageBases64, description);
  };

  return (
    <div className="bg-white dark:bg-slate-800 p-6 sm:p-8 rounded-2xl shadow-lg transition-all">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <h2 className="text-xl font-semibold text-slate-800 dark:text-slate-100 mb-2">
            1. Upload Product Images
          </h2>
          <p className="text-sm text-slate-500 dark:text-slate-400 mb-4">
            Clear photos from multiple angles help the AI identify your item accurately.
          </p>
          <ImageUploader onImageUpload={setImageBases64} />
        </div>

        <div>
          <h2 className="text-xl font-semibold text-slate-800 dark:text-slate-100 mb-2">
            2. Describe Your Item
          </h2>
          <p className="text-sm text-slate-500 dark:text-slate-400 mb-4">
            Add any details you know, like brand, condition, or history. Even a few words help!
          </p>
          <textarea
            value={description}
            onChange={e => setDescription(e.target.value)}
            placeholder="e.g., 'Old film camera from my grandpa, seems to be in good shape. Unsure if it works.'"
            className="w-full h-32 p-3 bg-slate-100 dark:bg-slate-700 rounded-lg border-2 border-dashed border-slate-300 dark:border-slate-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
            aria-label="Product Description"
          />
        </div>

        {error && <p className="text-sm text-red-500 dark:text-red-400 text-center">{error}</p>}

        <div className="pt-4">
          <button
            type="submit"
            disabled={imageBases64.length === 0 || !description.trim()}
            className="w-full flex items-center justify-center gap-2 bg-primary-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-4 focus:ring-primary-500/50 transition-all disabled:bg-slate-400 dark:disabled:bg-slate-600 disabled:cursor-not-allowed"
          >
            <MagnifyingGlassIcon className="w-5 h-5" />
            Analyze Product
          </button>
        </div>
      </form>
    </div>
  );
};
