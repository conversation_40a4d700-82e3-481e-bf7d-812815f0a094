import React, { useState } from 'react';
import { ComputerModel, ComputerVariation } from '../types.ts';
import { MagnifyingGlassIcon, ComputerDesktopIcon, CheckIcon } from './icons.tsx';

interface ComputerLookupProps {
  onSpecsFound: (computer: ComputerModel, selectedVariation: ComputerVariation) => void;
}

export const ComputerLookup: React.FC<ComputerLookupProps> = ({ onSpecsFound }) => {
  const [brand, setBrand] = useState('');
  const [model, setModel] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResult, setSearchResult] = useState<ComputerModel | null>(null);
  const [selectedVariation, setSelectedVariation] = useState<ComputerVariation | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [searchSource, setSearchSource] = useState<'database' | 'api' | null>(null);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!brand.trim() || !model.trim()) {
      setError('Please enter both brand and model.');
      return;
    }

    setIsSearching(true);
    setError(null);
    setSearchResult(null);
    setSelectedVariation(null);

    try {
      // Import the lookup function dynamically to avoid circular dependencies
      const { lookupComputerSpecs } = await import('../services/geminiService.ts');
      const result = await lookupComputerSpecs(brand.trim(), model.trim());

      if (result.found && result.computer) {
        setSearchResult(result.computer);
        setSearchSource(result.source);
        // Auto-select first variation if only one exists
        if (result.computer.variations.length === 1) {
          setSelectedVariation(result.computer.variations[0]);
        }
      } else {
        setError('Computer model not found. Please check the brand and model name.');
      }
    } catch (err) {
      console.error('Lookup error:', err);
      setError('An error occurred while searching for specifications. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const handleVariationSelect = (variation: ComputerVariation) => {
    setSelectedVariation(variation);
  };

  const handleProceedToAnalysis = () => {
    if (searchResult && selectedVariation) {
      onSpecsFound(searchResult, selectedVariation);
    }
  };

  const formatSpecs = (specs: Record<string, any>) => {
    return Object.entries(specs).map(([key, value]) => (
      <div key={key} className="flex justify-between text-sm">
        <span className="text-slate-600 dark:text-slate-400 capitalize">
          {key.replace(/_/g, ' ')}:
        </span>
        <span className="text-slate-800 dark:text-slate-200 font-medium">
          {Array.isArray(value) ? value.join(', ') : String(value)}
        </span>
      </div>
    ));
  };

  return (
    <div className="bg-white dark:bg-slate-800 p-6 sm:p-8 rounded-2xl shadow-lg">
      <div className="text-center mb-6">
        <ComputerDesktopIcon className="w-12 h-12 text-primary-500 mx-auto mb-3" />
        <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-100">
          Computer Specs Lookup
        </h2>
        <p className="text-slate-600 dark:text-slate-300 mt-2">
          Find detailed specifications for any computer model
        </p>
      </div>

      {/* Search Form */}
      <form onSubmit={handleSearch} className="space-y-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Brand *
            </label>
            <input
              type="text"
              value={brand}
              onChange={e => setBrand(e.target.value)}
              placeholder="e.g., Dell, HP, Apple, Lenovo"
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
              disabled={isSearching}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Model *
            </label>
            <input
              type="text"
              value={model}
              onChange={e => setModel(e.target.value)}
              placeholder="e.g., XPS 13, ThinkPad X1, MacBook Pro"
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
              disabled={isSearching}
            />
          </div>
        </div>

        {error && <p className="text-sm text-red-500 dark:text-red-400 text-center">{error}</p>}

        <div className="pt-2">
          <button
            type="submit"
            disabled={isSearching || !brand.trim() || !model.trim()}
            className="w-full flex items-center justify-center gap-2 bg-primary-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-4 focus:ring-primary-500/50 transition-all disabled:bg-slate-400 dark:disabled:bg-slate-600 disabled:cursor-not-allowed"
          >
            <MagnifyingGlassIcon className="w-5 h-5" />
            {isSearching ? 'Searching...' : 'Find Specifications'}
          </button>
        </div>
      </form>

      {/* Search Results */}
      {searchResult && (
        <div className="border-t border-slate-200 dark:border-slate-700 pt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
              {searchResult.brand} {searchResult.model}
            </h3>
            <div className="flex items-center gap-2">
              <span
                className={`text-xs px-2 py-1 rounded-full ${
                  searchSource === 'database'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                    : 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300'
                }`}
              >
                {searchSource === 'database' ? 'Cached' : 'Live Search'}
              </span>
              <span className="text-xs text-slate-500 dark:text-slate-400 capitalize">
                {searchResult.type}
              </span>
              {searchResult.release_year && (
                <span className="text-xs text-slate-500 dark:text-slate-400">
                  {searchResult.release_year}
                </span>
              )}
            </div>
          </div>

          {/* Variations */}
          <div className="space-y-3">
            <h4 className="font-medium text-slate-700 dark:text-slate-300">
              Available Configurations ({searchResult.variations.length})
            </h4>

            {searchResult.variations.map((variation, index) => (
              <div
                key={index}
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  selectedVariation === variation
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-slate-200 dark:border-slate-600 hover:border-primary-300'
                }`}
                onClick={() => handleVariationSelect(variation)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h5 className="font-medium text-slate-800 dark:text-slate-100">
                        {variation.variation_name}
                      </h5>
                      {selectedVariation === variation && (
                        <CheckIcon className="w-4 h-4 text-primary-500" />
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                      <div>
                        <p className="text-slate-600 dark:text-slate-400">Processor:</p>
                        <p className="font-medium text-slate-800 dark:text-slate-200">
                          {variation.processor}
                        </p>
                      </div>

                      <div>
                        <p className="text-slate-600 dark:text-slate-400">RAM Options:</p>
                        <p className="font-medium text-slate-800 dark:text-slate-200">
                          {variation.ram_options.join(', ')}
                        </p>
                      </div>

                      <div>
                        <p className="text-slate-600 dark:text-slate-400">Storage:</p>
                        <p className="font-medium text-slate-800 dark:text-slate-200">
                          {variation.storage_options.join(', ')}
                        </p>
                      </div>

                      <div>
                        <p className="text-slate-600 dark:text-slate-400">Graphics:</p>
                        <p className="font-medium text-slate-800 dark:text-slate-200">
                          {variation.graphics_options.join(', ')}
                        </p>
                      </div>
                    </div>

                    {/* Additional specs */}
                    {Object.keys(variation.additional_specs).length > 0 && (
                      <div className="mt-3 pt-3 border-t border-slate-200 dark:border-slate-600">
                        <p className="text-xs text-slate-500 dark:text-slate-400 mb-2">
                          Additional Specifications:
                        </p>
                        <div className="space-y-1">{formatSpecs(variation.additional_specs)}</div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Proceed Button */}
          {selectedVariation && (
            <div className="mt-6 pt-4 border-t border-slate-200 dark:border-slate-700">
              <button
                onClick={handleProceedToAnalysis}
                className="w-full bg-green-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-4 focus:ring-green-500/50 transition-all"
              >
                Use These Specs for Pricing Analysis
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
