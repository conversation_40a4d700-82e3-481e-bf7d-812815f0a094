import { getDatabase } from './databaseService.ts';
import { getExpandedDatabase } from './expandedDatabaseService.ts';
import type { ComputerModel, Device } from '../types.ts';

class MigrationService {
  private oldDb = getDatabase();
  private newDb = getExpandedDatabase();

  async migrateExistingData(): Promise<{ success: boolean; migrated: number; errors: string[] }> {
    console.log('Starting database migration...');

    const result = {
      success: true,
      migrated: 0,
      errors: [] as string[],
    };

    try {
      // Check if migration has already been done
      const migrationFlag = localStorage.getItem('database_migrated');
      if (migrationFlag === 'true') {
        console.log('Database already migrated');
        return result;
      }

      // Get all computers from old database
      const computers = await this.getAllComputersFromOldDb();
      console.log(`Found ${computers.length} computers to migrate`);

      for (const computer of computers) {
        try {
          await this.migrateComputer(computer);
          result.migrated++;
        } catch (error) {
          const errorMsg = `Failed to migrate ${computer.brand} ${computer.model}: ${error}`;
          console.error(errorMsg);
          result.errors.push(errorMsg);
        }
      }

      // Mark migration as complete
      localStorage.setItem('database_migrated', 'true');
      console.log(
        `Migration completed: ${result.migrated} computers migrated, ${result.errors.length} errors`
      );
    } catch (error) {
      result.success = false;
      result.errors.push(`Migration failed: ${error}`);
      console.error('Migration failed:', error);
    }

    return result;
  }

  private async getAllComputersFromOldDb(): Promise<ComputerModel[]> {
    // This is a simplified approach - in a real scenario we'd need to query the old database
    // For now, we'll check if there's any data in localStorage from the old system
    const oldDbData = localStorage.getItem('computer_specs_db');
    if (!oldDbData) {
      return [];
    }

    try {
      // Try to get popular searches to find computers that were looked up
      const popularSearches = await this.oldDb.getPopularSearches(100);
      const computers: ComputerModel[] = [];

      for (const search of popularSearches) {
        const [brand, model] = search.query.split(' ');
        if (brand && model) {
          const computer = await this.oldDb.findComputer(brand, model);
          if (computer) {
            computers.push(computer);
          }
        }
      }

      return computers;
    } catch (error) {
      console.error('Error getting computers from old database:', error);
      return [];
    }
  }

  private async migrateComputer(computer: ComputerModel): Promise<void> {
    // Convert ComputerModel to Device format
    const device: Omit<Device, 'id' | 'created_at' | 'updated_at'> = {
      category: 'computer',
      brand: computer.brand,
      model: computer.model,
      release_year: computer.release_year,
      discontinued: false,
      specifications: {
        type: computer.type,
        // Add any other specifications from the computer model
      },
      variations:
        computer.variations?.map(variation => ({
          variation_name: variation.variation_name,
          specifications: {
            processor: variation.processor,
            ram_options: variation.ram_options,
            storage_options: variation.storage_options,
            graphics_options: variation.graphics_options,
            screen_size: variation.screen_size,
            ...variation.additional_specs,
          },
        })) || [],
    };

    await this.newDb.saveDevice(device);
  }

  async checkMigrationStatus(): Promise<{ needed: boolean; oldDataExists: boolean }> {
    const migrationFlag = localStorage.getItem('database_migrated');
    const oldDbData = localStorage.getItem('computer_specs_db');

    return {
      needed: migrationFlag !== 'true' && !!oldDbData,
      oldDataExists: !!oldDbData,
    };
  }

  async resetMigration(): Promise<void> {
    localStorage.removeItem('database_migrated');
    console.log('Migration flag reset - migration will run again on next startup');
  }
}

// Singleton instance
let migrationInstance: MigrationService | null = null;

export function getMigrationService(): MigrationService {
  if (!migrationInstance) {
    migrationInstance = new MigrationService();
  }
  return migrationInstance;
}

export { MigrationService };
