import initSqlJs from 'sql.js';
import type { ComputerModel, ComputerVariation } from '../types.ts';

class DatabaseService {
  private db: any;
  private isInitialized: boolean = false;

  constructor() {
    this.initializeDatabase();
  }

  private async initializeDatabase() {
    if (this.isInitialized) return;

    try {
      const SQL = await initSqlJs({
        locateFile: (file: string) => `https://sql.js.org/dist/${file}`,
      });

      // Try to load existing database from localStorage
      const savedDb = localStorage.getItem('computer_specs_db');
      if (savedDb) {
        const uint8Array = new Uint8Array(JSON.parse(savedDb));
        this.db = new SQL.Database(uint8Array);
      } else {
        this.db = new SQL.Database();
      }

      this.createTables();
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  private async ensureInitialized() {
    if (!this.isInitialized) {
      await this.initializeDatabase();
    }
  }

  private saveToLocalStorage() {
    try {
      const data = this.db.export();
      const dataArray = Array.from(data);
      localStorage.setItem('computer_specs_db', JSON.stringify(dataArray));
    } catch (error) {
      console.error('Failed to save database to localStorage:', error);
    }
  }

  private createTables() {
    // Create computers table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS computers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        brand TEXT NOT NULL,
        model TEXT NOT NULL,
        type TEXT NOT NULL,
        release_year INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create computer_variations table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS computer_variations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        computer_id INTEGER NOT NULL,
        variation_name TEXT NOT NULL,
        processor TEXT NOT NULL,
        ram_options TEXT NOT NULL,
        storage_options TEXT NOT NULL,
        graphics_options TEXT NOT NULL,
        screen_size TEXT,
        additional_specs TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create search_cache table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS search_cache (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        search_query TEXT NOT NULL,
        computer_id INTEGER,
        search_count INTEGER DEFAULT 1,
        last_searched DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create indexes for better performance
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_computers_brand_model ON computers (brand, model);
      CREATE INDEX IF NOT EXISTS idx_search_cache_query ON search_cache (search_query);
      CREATE INDEX IF NOT EXISTS idx_variations_computer_id ON computer_variations (computer_id);
    `);
  }

  normalizeSearchQuery(brand: string, model: string): string {
    return `${brand.toLowerCase().trim()} ${model.toLowerCase().trim()}`;
  }

  async findComputer(brand: string, model: string): Promise<ComputerModel | null> {
    await this.ensureInitialized();

    const query = this.normalizeSearchQuery(brand, model);

    try {
      // Update search cache
      const existingCache = this.db.exec(
        `
        SELECT search_count FROM search_cache WHERE search_query = ?
      `,
        [query]
      );

      if (existingCache.length > 0 && existingCache[0].values.length > 0) {
        // Update existing
        this.db.exec(
          `
          UPDATE search_cache
          SET search_count = search_count + 1, last_searched = CURRENT_TIMESTAMP
          WHERE search_query = ?
        `,
          [query]
        );
      } else {
        // Insert new
        this.db.exec(
          `
          INSERT INTO search_cache (search_query, search_count, last_searched)
          VALUES (?, 1, CURRENT_TIMESTAMP)
        `,
          [query]
        );
      }

      // Find computer
      const computerResult = this.db.exec(
        `
        SELECT * FROM computers
        WHERE LOWER(brand) = ? AND LOWER(model) = ?
      `,
        [brand.toLowerCase(), model.toLowerCase()]
      );

      if (!computerResult.length || !computerResult[0].values.length) {
        return null;
      }

      const computerRow = computerResult[0].values[0];
      const computer = {
        id: computerRow[0],
        brand: computerRow[1],
        model: computerRow[2],
        type: computerRow[3],
        release_year: computerRow[4],
        created_at: computerRow[5],
        updated_at: computerRow[6],
      };

      // Get variations
      const variationsResult = this.db.exec(
        `
        SELECT * FROM computer_variations
        WHERE computer_id = ?
        ORDER BY id
      `,
        [computer.id]
      );

      const variations =
        variationsResult.length > 0
          ? variationsResult[0].values.map((row: any) => ({
              id: row[0],
              computer_id: row[1],
              variation_name: row[2],
              processor: row[3],
              ram_options: JSON.parse(row[4]),
              storage_options: JSON.parse(row[5]),
              graphics_options: JSON.parse(row[6]),
              screen_size: row[7],
              additional_specs: JSON.parse(row[8]),
              created_at: row[9],
            }))
          : [];

      this.saveToLocalStorage();

      return {
        ...computer,
        variations,
      } as ComputerModel;
    } catch (error) {
      console.error('Error finding computer:', error);
      return null;
    }
  }

  async saveComputer(
    computerData: Omit<ComputerModel, 'id' | 'created_at' | 'updated_at'>
  ): Promise<ComputerModel> {
    await this.ensureInitialized();

    try {
      // Check if computer already exists
      const existingResult = this.db.exec(
        `
        SELECT id FROM computers WHERE LOWER(brand) = ? AND LOWER(model) = ?
      `,
        [computerData.brand.toLowerCase(), computerData.model.toLowerCase()]
      );

      let computerId: number;

      if (existingResult.length > 0 && existingResult[0].values.length > 0) {
        // Update existing computer
        computerId = existingResult[0].values[0][0] as number;
        this.db.exec(
          `
          UPDATE computers
          SET type = ?, release_year = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `,
          [computerData.type, computerData.release_year, computerId]
        );

        // Delete existing variations
        this.db.exec(`DELETE FROM computer_variations WHERE computer_id = ?`, [computerId]);
      } else {
        // Insert new computer
        this.db.exec(
          `
          INSERT INTO computers (brand, model, type, release_year)
          VALUES (?, ?, ?, ?)
        `,
          [computerData.brand, computerData.model, computerData.type, computerData.release_year]
        );

        // Get the inserted ID
        const idResult = this.db.exec(`SELECT last_insert_rowid()`);
        computerId = idResult[0].values[0][0] as number;
      }

      // Insert variations
      for (const variation of computerData.variations) {
        this.db.exec(
          `
          INSERT INTO computer_variations (
            computer_id, variation_name, processor, ram_options,
            storage_options, graphics_options, screen_size, additional_specs
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `,
          [
            computerId,
            variation.variation_name,
            variation.processor,
            JSON.stringify(variation.ram_options),
            JSON.stringify(variation.storage_options),
            JSON.stringify(variation.graphics_options),
            variation.screen_size || null,
            JSON.stringify(variation.additional_specs),
          ]
        );
      }

      // Update search cache with computer_id
      const query = this.normalizeSearchQuery(computerData.brand, computerData.model);
      this.db.exec(
        `
        UPDATE search_cache
        SET computer_id = ?
        WHERE search_query = ?
      `,
        [computerId, query]
      );

      this.saveToLocalStorage();

      return {
        id: computerId,
        brand: computerData.brand,
        model: computerData.model,
        type: computerData.type,
        release_year: computerData.release_year,
        variations: computerData.variations,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error saving computer:', error);
      throw error;
    }
  }

  async getPopularSearches(limit: number = 10): Promise<Array<{ query: string; count: number }>> {
    await this.ensureInitialized();

    try {
      const result = this.db.exec(
        `
        SELECT search_query as query, search_count as count
        FROM search_cache
        ORDER BY search_count DESC, last_searched DESC
        LIMIT ?
      `,
        [limit]
      );

      if (!result.length) return [];

      return result[0].values.map((row: any) => ({
        query: row[0],
        count: row[1],
      }));
    } catch (error) {
      console.error('Error getting popular searches:', error);
      return [];
    }
  }

  async getRecentSearches(
    limit: number = 10
  ): Promise<Array<{ query: string; last_searched: string }>> {
    await this.ensureInitialized();

    try {
      const result = this.db.exec(
        `
        SELECT search_query as query, last_searched
        FROM search_cache
        ORDER BY last_searched DESC
        LIMIT ?
      `,
        [limit]
      );

      if (!result.length) return [];

      return result[0].values.map((row: any) => ({
        query: row[0],
        last_searched: row[1],
      }));
    } catch (error) {
      console.error('Error getting recent searches:', error);
      return [];
    }
  }

  close() {
    if (this.db) {
      this.saveToLocalStorage();
      this.db.close();
    }
  }
}

// Singleton instance
let dbInstance: DatabaseService | null = null;

export function getDatabase(): DatabaseService {
  if (!dbInstance) {
    dbInstance = new DatabaseService();
  }
  return dbInstance;
}

export { DatabaseService };
