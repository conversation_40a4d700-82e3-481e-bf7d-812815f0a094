import React, { useState, useEffect } from 'react';
import { DeviceCategory, ComponentType, SearchFilter, Device, Component } from '../types';
import { FunnelIcon, XMarkIcon, BookmarkIcon, ClockIcon, MagnifyingGlassIcon } from './icons';

interface SavedSearch {
  id: string;
  name: string;
  filter: SearchFilter;
  createdAt: string;
  lastUsed: string;
}

interface AdvancedSearchPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSearch: (filter: SearchFilter) => void;
  currentFilter: SearchFilter;
  searchType: 'devices' | 'components';
  availableBrands: string[];
}

export const AdvancedSearchPanel: React.FC<AdvancedSearchPanelProps> = ({
  isOpen,
  onClose,
  onSearch,
  currentFilter,
  searchType,
  availableBrands,
}) => {
  const [filter, setFilter] = useState<SearchFilter>(currentFilter);
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([]);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [searchName, setSearchName] = useState('');

  // Load saved searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('savedSearches');
    if (saved) {
      setSavedSearches(JSON.parse(saved));
    }
  }, []);

  // Save searches to localStorage
  const saveSavedSearches = (searches: SavedSearch[]) => {
    localStorage.setItem('savedSearches', JSON.stringify(searches));
    setSavedSearches(searches);
  };

  const handleFilterChange = (key: keyof SearchFilter, value: any) => {
    const newFilter = { ...filter, [key]: value };
    setFilter(newFilter);
  };

  const handleSearch = () => {
    onSearch(filter);
  };

  const clearFilters = () => {
    const emptyFilter: SearchFilter = {};
    setFilter(emptyFilter);
    onSearch(emptyFilter);
  };

  const saveCurrentSearch = () => {
    if (!searchName.trim()) return;

    const newSearch: SavedSearch = {
      id: Date.now().toString(),
      name: searchName.trim(),
      filter: { ...filter },
      createdAt: new Date().toISOString(),
      lastUsed: new Date().toISOString(),
    };

    const updatedSearches = [newSearch, ...savedSearches.slice(0, 9)]; // Keep only 10 most recent
    saveSavedSearches(updatedSearches);
    setSearchName('');
    setShowSaveDialog(false);
  };

  const loadSavedSearch = (savedSearch: SavedSearch) => {
    setFilter(savedSearch.filter);
    onSearch(savedSearch.filter);

    // Update last used timestamp
    const updatedSearches = savedSearches.map(s =>
      s.id === savedSearch.id ? { ...s, lastUsed: new Date().toISOString() } : s
    );
    saveSavedSearches(updatedSearches);
  };

  const deleteSavedSearch = (id: string) => {
    const updatedSearches = savedSearches.filter(s => s.id !== id);
    saveSavedSearches(updatedSearches);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700">
          <div className="flex items-center gap-3">
            <FunnelIcon className="w-6 h-6 text-primary-500" />
            <h2 className="text-xl font-bold text-slate-800 dark:text-slate-100">
              Advanced Search & Filters
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Sidebar - Saved Searches */}
          <div className="w-80 border-r border-slate-200 dark:border-slate-700 p-4 overflow-y-auto">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100 mb-3">
                Saved Searches
              </h3>
              <button
                onClick={() => setShowSaveDialog(true)}
                className="w-full flex items-center gap-2 px-3 py-2 text-sm bg-primary-100 text-primary-700 dark:bg-primary-900/20 dark:text-primary-300 rounded-lg hover:bg-primary-200 dark:hover:bg-primary-900/30 transition-colors"
              >
                <BookmarkIcon className="w-4 h-4" />
                Save Current Search
              </button>
            </div>

            {/* Save Dialog */}
            {showSaveDialog && (
              <div className="mb-4 p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                <input
                  type="text"
                  placeholder="Search name..."
                  value={searchName}
                  onChange={e => setSearchName(e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-slate-300 dark:border-slate-600 rounded-lg mb-2 dark:bg-slate-800"
                  onKeyPress={e => e.key === 'Enter' && saveCurrentSearch()}
                />
                <div className="flex gap-2">
                  <button
                    onClick={saveCurrentSearch}
                    disabled={!searchName.trim()}
                    className="flex-1 px-3 py-1 text-sm bg-primary-600 text-white rounded disabled:opacity-50"
                  >
                    Save
                  </button>
                  <button
                    onClick={() => setShowSaveDialog(false)}
                    className="flex-1 px-3 py-1 text-sm bg-slate-300 text-slate-700 rounded"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}

            {/* Saved Searches List */}
            <div className="space-y-2">
              {savedSearches.map(savedSearch => (
                <div key={savedSearch.id} className="p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <button
                        onClick={() => loadSavedSearch(savedSearch)}
                        className="text-left w-full"
                      >
                        <div className="font-medium text-slate-800 dark:text-slate-100 text-sm">
                          {savedSearch.name}
                        </div>
                        <div className="flex items-center gap-1 text-xs text-slate-500 dark:text-slate-400 mt-1">
                          <ClockIcon className="w-3 h-3" />
                          {new Date(savedSearch.lastUsed).toLocaleDateString()}
                        </div>
                      </button>
                    </div>
                    <button
                      onClick={() => deleteSavedSearch(savedSearch.id)}
                      className="text-slate-400 hover:text-red-500 ml-2"
                    >
                      <XMarkIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Main Filter Panel */}
          <div className="flex-1 p-6 overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Text Search */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Search Text
                </label>
                <input
                  type="text"
                  placeholder="Search in titles, descriptions, specifications..."
                  value={filter.text_search || ''}
                  onChange={e => handleFilterChange('text_search', e.target.value)}
                  className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
                />
              </div>

              {/* Category/Component Type Filter */}
              {searchType === 'devices' ? (
                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Device Categories
                  </label>
                  <div className="space-y-1 max-h-40 overflow-y-auto border border-slate-200 dark:border-slate-600 rounded-lg p-3">
                    {[
                      'computer',
                      'smartphone',
                      'tablet',
                      'gaming_console',
                      'audio_equipment',
                      'networking_hardware',
                      'camera',
                      'wearable',
                      'smart_home',
                    ].map(category => (
                      <label key={category} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filter.category?.includes(category as DeviceCategory) || false}
                          onChange={e => {
                            const current = filter.category || [];
                            const updated = e.target.checked
                              ? [...current, category as DeviceCategory]
                              : current.filter(c => c !== category);
                            handleFilterChange('category', updated);
                          }}
                          className="mr-2"
                        />
                        <span className="text-sm text-slate-600 dark:text-slate-400 capitalize">
                          {category.replace('_', ' ')}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              ) : (
                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Component Types
                  </label>
                  <div className="space-y-1 max-h-40 overflow-y-auto border border-slate-200 dark:border-slate-600 rounded-lg p-3">
                    {[
                      'cpu',
                      'gpu',
                      'ram',
                      'storage',
                      'motherboard',
                      'psu',
                      'cooling',
                      'case',
                      'monitor',
                      'keyboard',
                      'mouse',
                      'headset',
                      'speaker',
                      'webcam',
                    ].map(type => (
                      <label key={type} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filter.component_type?.includes(type as ComponentType) || false}
                          onChange={e => {
                            const current = filter.component_type || [];
                            const updated = e.target.checked
                              ? [...current, type as ComponentType]
                              : current.filter(t => t !== type);
                            handleFilterChange('component_type', updated);
                          }}
                          className="mr-2"
                        />
                        <span className="text-sm text-slate-600 dark:text-slate-400 uppercase">
                          {type}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              )}

              {/* Brand Filter */}
              <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Brands
                </label>
                <div className="space-y-1 max-h-40 overflow-y-auto border border-slate-200 dark:border-slate-600 rounded-lg p-3">
                  {availableBrands.slice(0, 20).map(brand => (
                    <label key={brand} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filter.brand?.includes(brand) || false}
                        onChange={e => {
                          const current = filter.brand || [];
                          const updated = e.target.checked
                            ? [...current, brand]
                            : current.filter(b => b !== brand);
                          handleFilterChange('brand', updated);
                        }}
                        className="mr-2"
                      />
                      <span className="text-sm text-slate-600 dark:text-slate-400">{brand}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Price Range */}
              <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Price Range (MSRP)
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    placeholder="Min"
                    value={filter.price_range?.[0] || ''}
                    onChange={e => {
                      const min = e.target.value ? parseInt(e.target.value) : undefined;
                      const max = filter.price_range?.[1];
                      handleFilterChange(
                        'price_range',
                        min !== undefined || max !== undefined
                          ? [min || 0, max || 999999]
                          : undefined
                      );
                    }}
                    className="px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
                  />
                  <input
                    type="number"
                    placeholder="Max"
                    value={filter.price_range?.[1] || ''}
                    onChange={e => {
                      const max = e.target.value ? parseInt(e.target.value) : undefined;
                      const min = filter.price_range?.[0];
                      handleFilterChange(
                        'price_range',
                        min !== undefined || max !== undefined
                          ? [min || 0, max || 999999]
                          : undefined
                      );
                    }}
                    className="px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
                  />
                </div>
              </div>

              {/* Release Year Range */}
              <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Release Year Range
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    placeholder="From"
                    min="1990"
                    max={new Date().getFullYear()}
                    value={filter.release_year_range?.[0] || ''}
                    onChange={e => {
                      const min = e.target.value ? parseInt(e.target.value) : undefined;
                      const max = filter.release_year_range?.[1];
                      handleFilterChange(
                        'release_year_range',
                        min !== undefined || max !== undefined
                          ? [min || 1990, max || new Date().getFullYear()]
                          : undefined
                      );
                    }}
                    className="px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
                  />
                  <input
                    type="number"
                    placeholder="To"
                    min="1990"
                    max={new Date().getFullYear()}
                    value={filter.release_year_range?.[1] || ''}
                    onChange={e => {
                      const max = e.target.value ? parseInt(e.target.value) : undefined;
                      const min = filter.release_year_range?.[0];
                      handleFilterChange(
                        'release_year_range',
                        min !== undefined || max !== undefined
                          ? [min || 1990, max || new Date().getFullYear()]
                          : undefined
                      );
                    }}
                    className="px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-slate-700 dark:text-slate-100"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-slate-200 dark:border-slate-700">
          <button
            onClick={clearFilters}
            className="px-4 py-2 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200"
          >
            Clear All Filters
          </button>
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700"
            >
              Cancel
            </button>
            <button
              onClick={handleSearch}
              className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
            >
              <MagnifyingGlassIcon className="w-4 h-4" />
              Apply Filters
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
