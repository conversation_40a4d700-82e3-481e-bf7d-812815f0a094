import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock environment variables
vi.mock('process', () => ({
  env: {
    API_KEY: 'test-api-key',
    GEMINI_API_KEY: 'test-gemini-api-key',
  },
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
vi.stubGlobal('localStorage', localStorageMock);

// Mock fetch
global.fetch = vi.fn();

// Mock SQL.js
vi.mock('sql.js', () => ({
  default: vi.fn(() =>
    Promise.resolve({
      Database: vi.fn(() => ({
        exec: vi.fn(),
        close: vi.fn(),
        export: vi.fn(() => new Uint8Array()),
      })),
    })
  ),
}));

// Mock Google GenAI
vi.mock('@google/genai', () => ({
  GoogleGenAI: vi.fn(() => ({
    models: {
      generateContent: vi.fn(() =>
        Promise.resolve({
          text: JSON.stringify({
            title: 'Test Product',
            description: 'Test description',
            estimatedPrice: '$100',
            condition: 'Good',
            searchResults: [],
          }),
        })
      ),
    },
  })),
}));

// Setup cleanup
afterEach(() => {
  vi.clearAllMocks();
});
