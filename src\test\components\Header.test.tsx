import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { Header } from '../../../components/Header';

describe('Header', () => {
  it('renders the application title', () => {
    render(<Header />);

    expect(screen.getByText('Resale Assistant AI')).toBeInTheDocument();
  });

  it('renders the subtitle', () => {
    render(<Header />);

    expect(
      screen.getByText('Identify, Describe, and Price Your Used Goods Instantly')
    ).toBeInTheDocument();
  });

  it('has proper semantic structure', () => {
    render(<Header />);

    const header = screen.getByRole('banner');
    expect(header).toBeInTheDocument();

    const heading = screen.getByRole('heading', { level: 1 });
    expect(heading).toHaveTextContent('Resale Assistant AI');
  });
});
