{"name": "resale-assistant-ai", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "prepare": "husky"}, "dependencies": {"@google/genai": "0.14.0", "react": "18.3.1", "react-dom": "18.3.1", "sql.js": "^1.13.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/node": "^22.14.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^16.1.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "typescript": "~5.8.2", "vite": "^6.3.5", "vitest": "^3.2.4"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}