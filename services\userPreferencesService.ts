import type { DeviceCategory, ComponentType, AnalysisMode } from '../types.ts';

export interface UserPreferences {
  // Display preferences
  theme: 'light' | 'dark' | 'auto';
  itemsPerPage: number;
  defaultView: 'grid' | 'list';

  // Default modes and categories
  defaultMode: AnalysisMode;
  favoriteCategories: DeviceCategory[];
  favoriteComponentTypes: ComponentType[];

  // Search preferences
  searchHistory: string[];
  maxSearchHistory: number;
  enableSearchSuggestions: boolean;

  // Database preferences
  autoBackup: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
  showAdvancedFilters: boolean;

  // Privacy preferences
  enableAnalytics: boolean;
  shareUsageData: boolean;
}

const DEFAULT_PREFERENCES: UserPreferences = {
  theme: 'auto',
  itemsPerPage: 20,
  defaultView: 'grid',
  defaultMode: 'general',
  favoriteCategories: [],
  favoriteComponentTypes: [],
  searchHistory: [],
  maxSearchHistory: 50,
  enableSearchSuggestions: true,
  autoBackup: false,
  backupFrequency: 'weekly',
  showAdvancedFilters: false,
  enableAnalytics: true,
  shareUsageData: false,
};

class UserPreferencesService {
  private preferences: UserPreferences;
  private readonly STORAGE_KEY = 'user_preferences';

  constructor() {
    this.preferences = this.loadPreferences();
  }

  private loadPreferences(): UserPreferences {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Merge with defaults to handle new preference fields
        return { ...DEFAULT_PREFERENCES, ...parsed };
      }
    } catch (error) {
      console.error('Failed to load user preferences:', error);
    }
    return { ...DEFAULT_PREFERENCES };
  }

  private savePreferences(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.preferences));
    } catch (error) {
      console.error('Failed to save user preferences:', error);
    }
  }

  // Get all preferences
  getPreferences(): UserPreferences {
    return { ...this.preferences };
  }

  // Update preferences
  updatePreferences(updates: Partial<UserPreferences>): void {
    this.preferences = { ...this.preferences, ...updates };
    this.savePreferences();
  }

  // Specific preference getters
  getTheme(): 'light' | 'dark' | 'auto' {
    return this.preferences.theme;
  }

  getItemsPerPage(): number {
    return this.preferences.itemsPerPage;
  }

  getDefaultMode(): AnalysisMode {
    return this.preferences.defaultMode;
  }

  getFavoriteCategories(): DeviceCategory[] {
    return [...this.preferences.favoriteCategories];
  }

  getFavoriteComponentTypes(): ComponentType[] {
    return [...this.preferences.favoriteComponentTypes];
  }

  getSearchHistory(): string[] {
    return [...this.preferences.searchHistory];
  }

  // Specific preference setters
  setTheme(theme: 'light' | 'dark' | 'auto'): void {
    this.updatePreferences({ theme });
  }

  setItemsPerPage(count: number): void {
    if (count > 0 && count <= 100) {
      this.updatePreferences({ itemsPerPage: count });
    }
  }

  setDefaultMode(mode: AnalysisMode): void {
    this.updatePreferences({ defaultMode: mode });
  }

  // Favorite management
  addFavoriteCategory(category: DeviceCategory): void {
    if (!this.preferences.favoriteCategories.includes(category)) {
      const favoriteCategories = [...this.preferences.favoriteCategories, category];
      this.updatePreferences({ favoriteCategories });
    }
  }

  removeFavoriteCategory(category: DeviceCategory): void {
    const favoriteCategories = this.preferences.favoriteCategories.filter(c => c !== category);
    this.updatePreferences({ favoriteCategories });
  }

  addFavoriteComponentType(type: ComponentType): void {
    if (!this.preferences.favoriteComponentTypes.includes(type)) {
      const favoriteComponentTypes = [...this.preferences.favoriteComponentTypes, type];
      this.updatePreferences({ favoriteComponentTypes });
    }
  }

  removeFavoriteComponentType(type: ComponentType): void {
    const favoriteComponentTypes = this.preferences.favoriteComponentTypes.filter(t => t !== type);
    this.updatePreferences({ favoriteComponentTypes });
  }

  // Search history management
  addToSearchHistory(query: string): void {
    if (!query.trim()) return;

    const searchHistory = [
      query.trim(),
      ...this.preferences.searchHistory.filter(h => h !== query.trim()),
    ].slice(0, this.preferences.maxSearchHistory);

    this.updatePreferences({ searchHistory });
  }

  clearSearchHistory(): void {
    this.updatePreferences({ searchHistory: [] });
  }

  getSearchSuggestions(query: string): string[] {
    if (!this.preferences.enableSearchSuggestions || !query.trim()) {
      return [];
    }

    const lowerQuery = query.toLowerCase();
    return this.preferences.searchHistory
      .filter(item => item.toLowerCase().includes(lowerQuery))
      .slice(0, 5);
  }

  // Reset to defaults
  resetToDefaults(): void {
    this.preferences = { ...DEFAULT_PREFERENCES };
    this.savePreferences();
  }

  // Export preferences
  exportPreferences(): string {
    return JSON.stringify(this.preferences, null, 2);
  }

  // Import preferences
  importPreferences(preferencesJson: string): boolean {
    try {
      const imported = JSON.parse(preferencesJson);
      // Validate the imported preferences
      if (this.validatePreferences(imported)) {
        this.preferences = { ...DEFAULT_PREFERENCES, ...imported };
        this.savePreferences();
        return true;
      }
    } catch (error) {
      console.error('Failed to import preferences:', error);
    }
    return false;
  }

  private validatePreferences(prefs: any): boolean {
    // Basic validation - could be expanded
    return (
      typeof prefs === 'object' &&
      prefs !== null &&
      (!prefs.theme || ['light', 'dark', 'auto'].includes(prefs.theme)) &&
      (!prefs.itemsPerPage || (typeof prefs.itemsPerPage === 'number' && prefs.itemsPerPage > 0))
    );
  }

  // Theme detection for auto mode
  getEffectiveTheme(): 'light' | 'dark' {
    if (this.preferences.theme === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return this.preferences.theme;
  }

  // Listen for system theme changes
  onThemeChange(callback: (theme: 'light' | 'dark') => void): () => void {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handler = () => {
      if (this.preferences.theme === 'auto') {
        callback(this.getEffectiveTheme());
      }
    };

    mediaQuery.addEventListener('change', handler);

    // Return cleanup function
    return () => mediaQuery.removeEventListener('change', handler);
  }
}

// Singleton instance
let preferencesInstance: UserPreferencesService | null = null;

export function getUserPreferences(): UserPreferencesService {
  if (!preferencesInstance) {
    preferencesInstance = new UserPreferencesService();
  }
  return preferencesInstance;
}

export { UserPreferencesService };
