import React from 'react';
import { Device, Component } from '../types.ts';
import { ArrowLeftIcon, CalendarIcon, CurrencyDollarIcon, TagIcon, ClockIcon } from './icons.tsx';

interface ItemDetailViewProps {
  item: Device | Component;
  onBack: () => void;
}

export const ItemDetailView: React.FC<ItemDetailViewProps> = ({ item, onBack }) => {
  const isDevice = 'category' in item;
  const isComponent = 'type' in item;

  const formatSpecifications = (specs: Record<string, any>) => {
    return Object.entries(specs).map(([key, value]) => (
      <div key={key} className="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
        <span className="text-slate-600 dark:text-slate-400 capitalize font-medium">
          {key.replace(/_/g, ' ')}
        </span>
        <span className="text-slate-800 dark:text-slate-200">
          {Array.isArray(value) ? value.join(', ') : String(value)}
        </span>
      </div>
    ));
  };

  const formatPerformanceMetrics = (metrics: Record<string, any> | null) => {
    if (!metrics) return null;
    
    return Object.entries(metrics).map(([key, value]) => (
      <div key={key} className="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
        <span className="text-slate-600 dark:text-slate-400 capitalize font-medium">
          {key.replace(/_/g, ' ')}
        </span>
        <span className="text-slate-800 dark:text-slate-200 font-mono">
          {String(value)}
        </span>
      </div>
    ));
  };

  return (
    <div className="bg-white dark:bg-slate-800 p-6 sm:p-8 rounded-2xl shadow-lg">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={onBack}
          className="p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors"
        >
          <ArrowLeftIcon className="w-5 h-5 text-slate-600 dark:text-slate-400" />
        </button>
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-slate-800 dark:text-slate-100">
            {item.brand} {item.model}
          </h1>
          <div className="flex items-center gap-2 mt-1">
            <span className={`text-xs px-2 py-1 rounded-full ${
              isDevice 
                ? 'bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-300'
                : 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
            }`}>
              {isDevice ? (item as Device).category.replace('_', ' ') : (item as Component).type.toUpperCase()}
            </span>
            {item.release_year && (
              <span className="text-xs text-slate-500 dark:text-slate-400 flex items-center gap-1">
                <CalendarIcon className="w-3 h-3" />
                {item.release_year}
              </span>
            )}
            {item.discontinued && (
              <span className="text-xs bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300 px-2 py-1 rounded-full">
                Discontinued
              </span>
            )}
          </div>
        </div>
        {item.msrp && (
          <div className="text-right">
            <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
              <CurrencyDollarIcon className="w-5 h-5" />
              <span className="text-2xl font-bold">{item.msrp}</span>
            </div>
            <span className="text-xs text-slate-500 dark:text-slate-400">MSRP</span>
          </div>
        )}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Specifications */}
        <div className="bg-slate-50 dark:bg-slate-700/50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-100 mb-4 flex items-center gap-2">
            <TagIcon className="w-5 h-5" />
            Specifications
          </h2>
          <div className="space-y-1">
            {formatSpecifications(item.specifications)}
          </div>
        </div>

        {/* Performance Metrics (for components) */}
        {isComponent && (item as Component).performance_metrics && (
          <div className="bg-slate-50 dark:bg-slate-700/50 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-100 mb-4 flex items-center gap-2">
              <ClockIcon className="w-5 h-5" />
              Performance Metrics
            </h2>
            <div className="space-y-1">
              {formatPerformanceMetrics((item as Component).performance_metrics)}
            </div>
          </div>
        )}

        {/* Compatibility (for components) */}
        {isComponent && (item as Component).compatibility && (item as Component).compatibility!.length > 0 && (
          <div className="bg-slate-50 dark:bg-slate-700/50 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-100 mb-4">
              Compatibility
            </h2>
            <div className="flex flex-wrap gap-2">
              {(item as Component).compatibility!.map((compat, index) => (
                <span
                  key={index}
                  className="text-xs bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300 px-2 py-1 rounded"
                >
                  {compat}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Variations (for devices) */}
        {isDevice && (item as Device).variations && (item as Device).variations!.length > 0 && (
          <div className="bg-slate-50 dark:bg-slate-700/50 p-4 rounded-lg lg:col-span-2">
            <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-100 mb-4">
              Available Variations ({(item as Device).variations!.length})
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {(item as Device).variations!.map((variation, index) => (
                <div key={index} className="bg-white dark:bg-slate-600 p-4 rounded-lg">
                  <h3 className="font-medium text-slate-800 dark:text-slate-100 mb-2">
                    {variation.variation_name}
                  </h3>
                  <div className="space-y-1">
                    {formatSpecifications(variation.specifications)}
                  </div>
                  {variation.price_difference !== undefined && variation.price_difference !== 0 && (
                    <div className="mt-3 pt-3 border-t border-slate-200 dark:border-slate-500">
                      <span className="text-sm text-slate-600 dark:text-slate-400">Price difference: </span>
                      <span className={`font-medium ${
                        variation.price_difference > 0 
                          ? 'text-red-600 dark:text-red-400' 
                          : 'text-green-600 dark:text-green-400'
                      }`}>
                        {variation.price_difference > 0 ? '+' : ''}${variation.price_difference}
                      </span>
                    </div>
                  )}
                  {variation.availability && (
                    <div className="mt-2">
                      <span className="text-sm text-slate-600 dark:text-slate-400">Availability: </span>
                      <span className="text-sm font-medium text-slate-800 dark:text-slate-200">
                        {variation.availability}
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Series Information (for components) */}
        {isComponent && (item as Component).series && (
          <div className="bg-slate-50 dark:bg-slate-700/50 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-100 mb-4">
              Series Information
            </h2>
            <div className="flex justify-between py-2">
              <span className="text-slate-600 dark:text-slate-400 font-medium">Product Series</span>
              <span className="text-slate-800 dark:text-slate-200">{(item as Component).series}</span>
            </div>
          </div>
        )}

        {/* Metadata */}
        <div className="bg-slate-50 dark:bg-slate-700/50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-100 mb-4">
            Database Information
          </h2>
          <div className="space-y-2">
            {item.created_at && (
              <div className="flex justify-between py-1">
                <span className="text-slate-600 dark:text-slate-400 text-sm">Added to database</span>
                <span className="text-slate-800 dark:text-slate-200 text-sm">
                  {new Date(item.created_at).toLocaleDateString()}
                </span>
              </div>
            )}
            {item.updated_at && (
              <div className="flex justify-between py-1">
                <span className="text-slate-600 dark:text-slate-400 text-sm">Last updated</span>
                <span className="text-slate-800 dark:text-slate-200 text-sm">
                  {new Date(item.updated_at).toLocaleDateString()}
                </span>
              </div>
            )}
            <div className="flex justify-between py-1">
              <span className="text-slate-600 dark:text-slate-400 text-sm">Item ID</span>
              <span className="text-slate-800 dark:text-slate-200 text-sm font-mono">
                #{item.id}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="mt-6 pt-6 border-t border-slate-200 dark:border-slate-700">
        <div className="flex gap-3">
          <button
            onClick={onBack}
            className="px-4 py-2 bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 rounded-lg hover:bg-slate-200 dark:hover:bg-slate-600 transition-colors"
          >
            Back to Database
          </button>
          <button
            className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            onClick={() => {
              // Could implement export functionality
              console.log('Export item:', item);
            }}
          >
            Export Data
          </button>
        </div>
      </div>
    </div>
  );
};
