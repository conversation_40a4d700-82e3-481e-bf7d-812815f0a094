import React, { useState, useCallback, useRef } from 'react';
import { UploadIcon, XCircleIcon, MagnifyingGlassIcon, ArrowLeftIcon } from './icons.tsx';

interface ImageData {
  id: string;
  base64: string;
  name: string;
  size: number;
  type: string;
  lastModified: number;
}

interface ImageUploaderProps {
  onImageUpload: (base64s: string[]) => void;
  maxImages?: number;
  maxFileSize?: number; // in MB
  allowedTypes?: string[];
  enableCompression?: boolean;
  compressionQuality?: number;
}

export const ImageUploader: React.FC<ImageUploaderProps> = ({
  onImageUpload,
  maxImages = 10,
  maxFileSize = 10, // 10MB default
  allowedTypes = ['image/png', 'image/jpeg', 'image/webp', 'image/gif'],
  enableCompression = true,
  compressionQuality = 0.8
}) => {
  const [images, setImages] = useState<ImageData[]>([]);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Image compression function
  const compressImage = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions (max 1920x1080 for performance)
        const maxWidth = 1920;
        const maxHeight = 1080;
        let { width, height } = img;

        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);
        const compressedBase64 = canvas.toDataURL('image/jpeg', compressionQuality);
        resolve(compressedBase64);
      };

      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  };

  const validateFile = (file: File): string | null => {
    if (!allowedTypes.includes(file.type)) {
      return `File type ${file.type} is not allowed. Please use: ${allowedTypes.join(', ')}`;
    }

    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size (${(file.size / 1024 / 1024).toFixed(1)}MB) exceeds maximum allowed size of ${maxFileSize}MB`;
    }

    if (images.length >= maxImages) {
      return `Maximum ${maxImages} images allowed`;
    }

    return null;
  };

  const handleFiles = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    setError(null);
    setIsUploading(true);
    setUploadProgress(0);

    const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'));
    if (imageFiles.length === 0) {
      setError('No valid image files found');
      setIsUploading(false);
      return;
    }

    try {
      const newImages: ImageData[] = [];

      for (let i = 0; i < imageFiles.length; i++) {
        const file = imageFiles[i];

        // Validate file
        const validationError = validateFile(file);
        if (validationError) {
          setError(validationError);
          continue;
        }

        // Update progress
        setUploadProgress((i / imageFiles.length) * 50);

        // Compress or read file
        let base64: string;
        if (enableCompression && file.type !== 'image/gif') {
          base64 = await compressImage(file);
        } else {
          base64 = await new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(file);
          });
        }

        const imageData: ImageData = {
          id: `${Date.now()}-${i}`,
          base64,
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: file.lastModified,
        };

        newImages.push(imageData);

        // Update progress
        setUploadProgress(((i + 1) / imageFiles.length) * 100);
      }

      const updatedImages = [...images, ...newImages];
      setImages(updatedImages);
      onImageUpload(updatedImages.map(img => img.base64));

    } catch (error) {
      console.error('Error processing files:', error);
      setError('Error processing files. Please try again.');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleRemoveImage = (indexToRemove: number) => {
    const updatedImages = images.filter((_, index) => index !== indexToRemove);
    setImages(updatedImages);
    onImageUpload(updatedImages.map(img => img.base64));
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const openImagePreview = (index: number) => {
    setSelectedImageIndex(index);
  };

  const closeImagePreview = () => {
    setSelectedImageIndex(null);
  };

  const navigatePreview = (direction: 'prev' | 'next') => {
    if (selectedImageIndex === null) return;

    if (direction === 'prev') {
      setSelectedImageIndex(selectedImageIndex > 0 ? selectedImageIndex - 1 : images.length - 1);
    } else {
      setSelectedImageIndex(selectedImageIndex < images.length - 1 ? selectedImageIndex + 1 : 0);
    }
  };

  const onFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(e.target.files);
    // Clear the value to allow re-uploading the same file after removal
    e.target.value = '';
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const handleDragEvents = useCallback(
    (e: React.DragEvent<HTMLLabelElement>, dragging: boolean) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(dragging);
    },
    []
  );

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLLabelElement>) => {
      handleDragEvents(e, false);
      if (e.dataTransfer.files && e.dataTransfer.files[0]) {
        handleFiles(e.dataTransfer.files);
      }
    },
    [handleDragEvents, handleFiles]
  );

  return (
    <div className="space-y-4">
      {/* Error Display */}
      {error && (
        <div className="p-3 bg-red-100 dark:bg-red-900/20 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 rounded-lg text-sm">
          {error}
        </div>
      )}

      {/* Upload Progress */}
      {isUploading && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-slate-600 dark:text-slate-400">
            <span>Processing images...</span>
            <span>{Math.round(uploadProgress)}%</span>
          </div>
          <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
            <div
              className="bg-primary-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* Image Previews */}
      {images.length > 0 && (
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300">
              Uploaded Images ({images.length}/{maxImages})
            </h4>
            <button
              onClick={triggerFileInput}
              disabled={images.length >= maxImages || isUploading}
              className="text-sm text-primary-600 hover:text-primary-700 disabled:text-slate-400 disabled:cursor-not-allowed"
            >
              Add More
            </button>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
            {images.map((image, index) => (
              <div key={image.id} className="relative group">
                <div className="aspect-square bg-slate-200 dark:bg-slate-600 rounded-lg overflow-hidden">
                  <img
                    src={image.base64}
                    alt={`Product preview ${index + 1}`}
                    className="object-cover h-full w-full cursor-pointer hover:scale-105 transition-transform"
                    onClick={() => openImagePreview(index)}
                  />
                </div>

                {/* Image Info Overlay */}
                <div className="absolute inset-x-0 bottom-0 bg-black bg-opacity-75 text-white text-xs p-2 rounded-b-lg opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="truncate">{image.name}</div>
                  <div>{formatFileSize(image.size)}</div>
                </div>

                {/* Action Buttons */}
                <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <button
                    type="button"
                    onClick={() => openImagePreview(index)}
                    className="bg-black bg-opacity-50 text-white p-1 rounded-full hover:bg-opacity-75 transition-all"
                    aria-label={`Preview image ${index + 1}`}
                  >
                    <MagnifyingGlassIcon className="w-4 h-4" />
                  </button>
                  <button
                    type="button"
                    onClick={() => handleRemoveImage(index)}
                    className="bg-red-600 bg-opacity-75 text-white p-1 rounded-full hover:bg-opacity-100 transition-all"
                    aria-label={`Remove image ${index + 1}`}
                  >
                    <XCircleIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      {/* Upload Area */}
      <label
        htmlFor="image-upload"
        className={`relative flex flex-col items-center justify-center w-full h-48 border-2 border-dashed rounded-lg cursor-pointer transition-all duration-200
        ${isDragging
          ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 scale-105'
          : images.length >= maxImages
            ? 'border-slate-200 dark:border-slate-700 bg-slate-100 dark:bg-slate-800 cursor-not-allowed opacity-50'
            : 'border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700/50 hover:bg-slate-100 dark:hover:bg-slate-700 hover:border-primary-400'
        }`}
        onDragEnter={e => handleDragEvents(e, true)}
        onDragLeave={e => handleDragEvents(e, false)}
        onDragOver={e => handleDragEvents(e, true)}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center justify-center pt-5 pb-6 text-center">
          <UploadIcon className={`w-8 h-8 mb-4 transition-colors ${isDragging ? 'text-primary-500' : 'text-slate-500 dark:text-slate-400'}`} />
          <p className="mb-2 text-sm text-slate-500 dark:text-slate-400">
            <span className="font-semibold">
              {images.length >= maxImages
                ? `Maximum ${maxImages} images reached`
                : images.length > 0
                  ? 'Add more images'
                  : 'Click to upload'
              }
            </span>
            {images.length < maxImages && (
              <span> or drag and drop</span>
            )}
          </p>
          <p className="text-xs text-slate-500 dark:text-slate-400">
            {allowedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')}
            {' • Max '}
            {maxFileSize}MB each
            {enableCompression && ' • Auto-compressed'}
          </p>
        </div>
        <input
          ref={fileInputRef}
          id="image-upload"
          type="file"
          className="hidden"
          accept={allowedTypes.join(', ')}
          onChange={onFileChange}
          multiple
          disabled={images.length >= maxImages || isUploading}
        />
      </label>

      {/* Image Preview Modal */}
      {selectedImageIndex !== null && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            {/* Close Button */}
            <button
              onClick={closeImagePreview}
              className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-all z-10"
            >
              <XCircleIcon className="w-6 h-6" />
            </button>

            {/* Navigation Buttons */}
            {images.length > 1 && (
              <>
                <button
                  onClick={() => navigatePreview('prev')}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-all z-10"
                >
                  <ArrowLeftIcon className="w-6 h-6" />
                </button>
                <button
                  onClick={() => navigatePreview('next')}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-all z-10"
                >
                  <ArrowLeftIcon className="w-6 h-6 transform rotate-180" />
                </button>
              </>
            )}

            {/* Image */}
            <img
              src={images[selectedImageIndex].base64}
              alt={`Preview ${selectedImageIndex + 1}`}
              className="max-w-full max-h-full object-contain rounded-lg"
            />

            {/* Image Info */}
            <div className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-75 text-white p-4 rounded-lg">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-medium truncate">{images[selectedImageIndex].name}</h3>
                  <p className="text-sm text-slate-300">
                    {formatFileSize(images[selectedImageIndex].size)} • {images[selectedImageIndex].type}
                  </p>
                </div>
                {images.length > 1 && (
                  <div className="text-sm text-slate-300">
                    {selectedImageIndex + 1} of {images.length}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
