
import React, { useState, useCallback } from 'react';
import { UploadIcon, XCircleIcon } from './icons.tsx';

interface ImageUploaderProps {
  onImageUpload: (base64s: string[]) => void;
}

export const ImageUploader: React.FC<ImageUploaderProps> = ({ onImageUpload }) => {
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [isDragging, setIsDragging] = useState<boolean>(false);

  const handleFiles = (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'));
    if (imageFiles.length === 0) return;

    const fileReadPromises = imageFiles.map(file => {
      return new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          resolve(reader.result as string);
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
    });

    Promise.all(fileReadPromises).then(newBase64Strings => {
      const updatedPreviews = [...imagePreviews, ...newBase64Strings];
      setImagePreviews(updatedPreviews);
      onImageUpload(updatedPreviews);
    }).catch(error => {
        console.error("Error reading files:", error);
    });
  };
  
  const handleRemoveImage = (indexToRemove: number) => {
    const updatedPreviews = imagePreviews.filter((_, index) => index !== indexToRemove);
    setImagePreviews(updatedPreviews);
    onImageUpload(updatedPreviews);
    const input = document.getElementById('image-upload') as HTMLInputElement;
    if(input) input.value = '';
  };

  const onFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(e.target.files);
    // Clear the value to allow re-uploading the same file after removal
    e.target.value = '';
  };
  
  const handleDragEvents = useCallback((e: React.DragEvent<HTMLLabelElement>, dragging: boolean) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(dragging);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent<HTMLLabelElement>) => {
    handleDragEvents(e, false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  }, [handleDragEvents, handleFiles]);

  return (
    <div className="space-y-4">
      {imagePreviews.length > 0 && (
        <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-4 p-2 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
          {imagePreviews.map((src, index) => (
            <div key={src.slice(-20)} className="relative group aspect-square">
              <img src={src} alt={`Product preview ${index + 1}`} className="object-cover h-full w-full rounded-lg shadow-md" />
              <button
                type="button"
                onClick={() => handleRemoveImage(index)}
                className="absolute -top-2 -right-2 text-red-600 bg-white dark:bg-slate-800 rounded-full p-0.5 transform transition-all group-hover:scale-110 opacity-75 group-hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                aria-label={`Remove image ${index + 1}`}
              >
                <XCircleIcon className="w-6 h-6" />
              </button>
            </div>
          ))}
        </div>
      )}
      <label
        htmlFor="image-upload"
        className={`relative flex flex-col items-center justify-center w-full h-48 border-2 border-dashed rounded-lg cursor-pointer transition-colors
        ${isDragging ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20' : 'border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700/50 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
        onDragEnter={(e) => handleDragEvents(e, true)}
        onDragLeave={(e) => handleDragEvents(e, false)}
        onDragOver={(e) => handleDragEvents(e, true)}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center justify-center pt-5 pb-6 text-center">
            <UploadIcon className="w-8 h-8 mb-4 text-slate-500 dark:text-slate-400" />
            <p className="mb-2 text-sm text-slate-500 dark:text-slate-400">
              <span className="font-semibold">{imagePreviews.length > 0 ? "Add more images" : "Click to upload"}</span> or drag and drop
            </p>
            <p className="text-xs text-slate-500 dark:text-slate-400">PNG, JPG, or WEBP</p>
        </div>
        <input id="image-upload" type="file" className="hidden" accept="image/png, image/jpeg, image/webp" onChange={onFileChange} multiple />
      </label>
    </div>
  );
};