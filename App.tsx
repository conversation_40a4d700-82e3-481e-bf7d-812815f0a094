import React, { useState, useCallback, useEffect, Suspense, lazy } from 'react';
import { Header } from './components/Header.tsx';
import { LoadingSpinner } from './components/LoadingSpinner.tsx';

// Lazy load components that are not immediately needed
const ProductForm = lazy(() => import('./components/ProductForm.tsx').then(module => ({ default: module.ProductForm })));
const ComputerForm = lazy(() => import('./components/ComputerForm.tsx').then(module => ({ default: module.ComputerForm })));
const ComputerLookup = lazy(() => import('./components/ComputerLookup.tsx').then(module => ({ default: module.ComputerLookup })));
const ComponentSearch = lazy(() => import('./components/ComponentSearch.tsx').then(module => ({ default: module.ComponentSearch })));
const DeviceSearch = lazy(() => import('./components/DeviceSearch.tsx').then(module => ({ default: module.DeviceSearch })));
const DatabaseBrowser = lazy(() => import('./components/DatabaseBrowser.tsx').then(module => ({ default: module.DatabaseBrowser })));
const ResultsDisplay = lazy(() => import('./components/ResultsDisplay.tsx').then(module => ({ default: module.ResultsDisplay })));

// Dynamic service imports
const loadGeminiService = () => import('./services/geminiService.ts');
import type {
  ProductAnalysis,
  AnalysisMode,
  ComputerSpecs,
  ComputerModel,
  ComputerVariation,
  Device,
  Component,
} from './types.ts';
import {
  ErrorIcon,
  ComputerDesktopIcon,
  TagIcon,
  MagnifyingGlassIcon,
  CpuChipIcon,
  DevicePhoneMobileIcon,
  DatabaseIcon,
} from './components/icons.tsx';

const App: React.FC = () => {
  const [analysisResult, setAnalysisResult] = useState<ProductAnalysis | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [mode, setMode] = useState<AnalysisMode>('general');

  // Initialize migration on app start
  useEffect(() => {
    const initializeMigration = async () => {
      try {
        const { getMigrationService } = await import('./services/migrationService.ts');
        const migrationService = getMigrationService();
        const status = await migrationService.checkMigrationStatus();

        if (status.needed) {
          console.log('Running database migration...');
          const result = await migrationService.migrateExistingData();
          if (result.success) {
            console.log(`Migration completed: ${result.migrated} items migrated`);
          } else {
            console.error('Migration failed:', result.errors);
          }
        }
      } catch (error) {
        console.error('Migration initialization failed:', error);
      }
    };

    initializeMigration();
  }, []);

  const handleAnalysis = useCallback(async (images: string[], description: string) => {
    setIsLoading(true);
    setError(null);
    setAnalysisResult(null);

    try {
      if (!process.env.API_KEY) {
        throw new Error('API key is not configured. Please set the API_KEY environment variable.');
      }
      const { identifyAndPriceProduct } = await loadGeminiService();
      const result = await identifyAndPriceProduct(images, description);
      setAnalysisResult(result);
    } catch (err) {
      console.error(err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleComputerAnalysis = useCallback(async (specs: ComputerSpecs) => {
    setIsLoading(true);
    setError(null);
    setAnalysisResult(null);

    try {
      if (!process.env.API_KEY) {
        throw new Error('API key is not configured. Please set the API_KEY environment variable.');
      }
      const { analyzeComputer } = await loadGeminiService();
      const result = await analyzeComputer(specs);
      setAnalysisResult(result);
    } catch (err) {
      console.error(err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleSpecsFound = useCallback(
    (computer: ComputerModel, selectedVariation: ComputerVariation) => {
      // Convert the selected variation to ComputerSpecs format
      const specs: ComputerSpecs = {
        type: computer.type,
        brand: computer.brand,
        model: computer.model,
        processor: selectedVariation.processor,
        ram: selectedVariation.ram_options.join(' / '),
        storage: selectedVariation.storage_options.join(' / '),
        graphics: selectedVariation.graphics_options.join(' / '),
        screenSize: selectedVariation.screen_size || '',
        condition: 'Good', // Default condition, user can modify
        additionalNotes: `${selectedVariation.variation_name}. Additional specs: ${JSON.stringify(selectedVariation.additional_specs)}`,
      };

      // Proceed directly to analysis with the found specs
      handleComputerAnalysis(specs);
    },
    [handleComputerAnalysis]
  );

  const handleComponentFound = useCallback((component: Component) => {
    console.log('Component added to database:', component);
    // Could show a success message or redirect to database view
    setMode('database');
  }, []);

  const handleDeviceFound = useCallback((device: Device) => {
    console.log('Device added to database:', device);
    // Could show a success message or redirect to database view
    setMode('database');
  }, []);

  const handleReset = useCallback(() => {
    setAnalysisResult(null);
    setError(null);
    setIsLoading(false);
  }, []);

  const handleModeChange = useCallback((newMode: AnalysisMode) => {
    setMode(newMode);
    setAnalysisResult(null);
    setError(null);
  }, []);

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900 text-slate-900 dark:text-slate-100 font-sans">
      <Header />
      <main className="container mx-auto px-4 py-8 md:py-12">
        <div className="max-w-4xl mx-auto">
          {!analysisResult && !isLoading && (
            <>
              {/* Mode Selection */}
              <div className="bg-white dark:bg-slate-800 p-6 rounded-2xl shadow-lg mb-6">
                <h2 className="text-xl font-bold text-slate-800 dark:text-slate-100 mb-4 text-center">
                  What would you like to do?
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <button
                    onClick={() => handleModeChange('general')}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      mode === 'general'
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-slate-200 dark:border-slate-600 hover:border-primary-300'
                    }`}
                  >
                    <TagIcon className="w-8 h-8 mx-auto mb-2 text-primary-500" />
                    <h3 className="font-semibold text-slate-800 dark:text-slate-100">
                      General Items
                    </h3>
                    <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                      Upload photos of any item for analysis
                    </p>
                  </button>
                  <button
                    onClick={() => handleModeChange('computer')}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      mode === 'computer'
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-slate-200 dark:border-slate-600 hover:border-primary-300'
                    }`}
                  >
                    <ComputerDesktopIcon className="w-8 h-8 mx-auto mb-2 text-primary-500" />
                    <h3 className="font-semibold text-slate-800 dark:text-slate-100">
                      Manual Entry
                    </h3>
                    <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                      Enter computer specs manually
                    </p>
                  </button>
                  <button
                    onClick={() => handleModeChange('lookup')}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      mode === 'lookup'
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-slate-200 dark:border-slate-600 hover:border-primary-300'
                    }`}
                  >
                    <MagnifyingGlassIcon className="w-8 h-8 mx-auto mb-2 text-primary-500" />
                    <h3 className="font-semibold text-slate-800 dark:text-slate-100">
                      Computer Lookup
                    </h3>
                    <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                      Find computer specs by model
                    </p>
                  </button>
                  <button
                    onClick={() => handleModeChange('component')}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      mode === 'component'
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-slate-200 dark:border-slate-600 hover:border-primary-300'
                    }`}
                  >
                    <CpuChipIcon className="w-8 h-8 mx-auto mb-2 text-primary-500" />
                    <h3 className="font-semibold text-slate-800 dark:text-slate-100">
                      Component Search
                    </h3>
                    <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                      Find hardware component specs
                    </p>
                  </button>
                  <button
                    onClick={() => handleModeChange('database')}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      mode === 'database'
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-slate-200 dark:border-slate-600 hover:border-primary-300'
                    }`}
                  >
                    <DatabaseIcon className="w-8 h-8 mx-auto mb-2 text-primary-500" />
                    <h3 className="font-semibold text-slate-800 dark:text-slate-100">
                      Browse Database
                    </h3>
                    <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                      View all stored items
                    </p>
                  </button>
                </div>
              </div>

              {/* Form based on selected mode */}
              {mode === 'general' && <ProductForm onAnalyze={handleAnalysis} />}
              {mode === 'computer' && <ComputerForm onAnalyze={handleComputerAnalysis} />}
              {mode === 'lookup' && <ComputerLookup onSpecsFound={handleSpecsFound} />}
              {mode === 'component' && <ComponentSearch onComponentFound={handleComponentFound} />}
              {mode === 'database' && <DatabaseBrowser />}
            </>
          )}

          {isLoading && <LoadingSpinner mode={mode} />}

          {error && (
            <div
              className="bg-red-100 dark:bg-red-900/20 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg relative text-center"
              role="alert"
            >
              <div className="flex items-center justify-center">
                <ErrorIcon className="w-6 h-6 mr-2" />
                <strong className="font-bold">Error:</strong>
                <span className="block sm:inline ml-2">{error}</span>
              </div>
            </div>
          )}

          {analysisResult && !isLoading && (
            <ResultsDisplay result={analysisResult} onReset={handleReset} />
          )}
        </div>
      </main>
      <footer className="text-center py-4 text-slate-500 dark:text-slate-400 text-sm">
        <p>Powered by AI. Always verify pricing and details before listing.</p>
      </footer>
    </div>
  );
};

export default App;
