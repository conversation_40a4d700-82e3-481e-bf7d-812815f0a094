import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi } from 'vitest';
import { AdvancedSearchPanel } from '../../../components/AdvancedSearchPanel';

describe('AdvancedSearchPanel', () => {
  const mockOnClose = vi.fn();
  const mockOnSearch = vi.fn();
  const defaultProps = {
    isOpen: true,
    onClose: mockOnClose,
    onSearch: mockOnSearch,
    currentFilter: {},
    searchType: 'devices' as const,
    availableBrands: ['Apple', 'Dell', 'HP'],
  };

  beforeEach(() => {
    mockOnClose.mockClear();
    mockOnSearch.mockClear();
  });

  it('renders when open', () => {
    render(<AdvancedSearchPanel {...defaultProps} />);
    
    expect(screen.getByText('Advanced Search & Filters')).toBeInTheDocument();
    expect(screen.getByText('Saved Searches')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search in titles, descriptions, specifications...')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(<AdvancedSearchPanel {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText('Advanced Search & Filters')).not.toBeInTheDocument();
  });

  it('shows device categories when searchType is devices', () => {
    render(<AdvancedSearchPanel {...defaultProps} searchType="devices" />);
    
    expect(screen.getByText('Device Categories')).toBeInTheDocument();
    expect(screen.getByText('computer')).toBeInTheDocument();
    expect(screen.getByText('smartphone')).toBeInTheDocument();
  });

  it('shows component types when searchType is components', () => {
    render(<AdvancedSearchPanel {...defaultProps} searchType="components" />);
    
    expect(screen.getByText('Component Types')).toBeInTheDocument();
    expect(screen.getByText('CPU')).toBeInTheDocument();
    expect(screen.getByText('GPU')).toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', async () => {
    const user = userEvent.setup();
    render(<AdvancedSearchPanel {...defaultProps} />);
    
    const closeButton = screen.getByRole('button', { name: /close/i });
    await user.click(closeButton);
    
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('calls onSearch when Apply Filters button is clicked', async () => {
    const user = userEvent.setup();
    render(<AdvancedSearchPanel {...defaultProps} />);
    
    const applyButton = screen.getByRole('button', { name: /apply filters/i });
    await user.click(applyButton);
    
    expect(mockOnSearch).toHaveBeenCalledTimes(1);
  });

  it('updates text search filter', async () => {
    const user = userEvent.setup();
    render(<AdvancedSearchPanel {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search in titles, descriptions, specifications...');
    await user.type(searchInput, 'test search');
    
    expect(searchInput).toHaveValue('test search');
  });

  it('shows available brands', () => {
    render(<AdvancedSearchPanel {...defaultProps} />);
    
    expect(screen.getByText('Apple')).toBeInTheDocument();
    expect(screen.getByText('Dell')).toBeInTheDocument();
    expect(screen.getByText('HP')).toBeInTheDocument();
  });

  it('has price range inputs', () => {
    render(<AdvancedSearchPanel {...defaultProps} />);
    
    expect(screen.getByPlaceholderText('Min')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Max')).toBeInTheDocument();
  });

  it('has release year range inputs', () => {
    render(<AdvancedSearchPanel {...defaultProps} />);
    
    expect(screen.getByPlaceholderText('From')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('To')).toBeInTheDocument();
  });
});
