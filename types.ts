export interface GroundingSource {
  uri: string;
  title: string;
}

export interface SearchResult {
  title: string;
  url: string;
  price: string;
  platform: string;
  condition: string;
  description: string;
}

export interface ComputerSpecs {
  type: 'laptop' | 'desktop' | 'all-in-one';
  brand: string;
  model: string;
  processor: string;
  ram: string;
  storage: string;
  graphics: string;
  screenSize?: string; // For laptops
  condition: string;
  additionalNotes: string;
}

export type AnalysisMode = 'general' | 'computer' | 'lookup' | 'component' | 'database';

export type DeviceCategory =
  | 'computer'
  | 'smartphone'
  | 'tablet'
  | 'gaming_console'
  | 'audio_equipment'
  | 'networking_hardware'
  | 'camera'
  | 'wearable'
  | 'smart_home'
  | 'component';

export type ComponentType =
  | 'cpu'
  | 'gpu'
  | 'ram'
  | 'storage'
  | 'motherboard'
  | 'psu'
  | 'cooling'
  | 'case'
  | 'monitor'
  | 'keyboard'
  | 'mouse'
  | 'headset'
  | 'speaker'
  | 'webcam';

export interface ComputerVariation {
  id?: number;
  computer_id?: number;
  variation_name: string;
  processor: string;
  ram_options: string[];
  storage_options: string[];
  graphics_options: string[];
  screen_size?: string;
  additional_specs: Record<string, any>;
  created_at?: string;
}

export interface ComputerModel {
  id?: number;
  brand: string;
  model: string;
  type: 'laptop' | 'desktop' | 'all-in-one';
  release_year?: number;
  variations: ComputerVariation[];
  created_at?: string;
  updated_at?: string;
}

export interface ComputerLookupResult {
  found: boolean;
  computer?: ComputerModel;
  source: 'database' | 'api';
}

// New expanded database schema interfaces
export interface Device {
  id?: number;
  category: DeviceCategory;
  brand: string;
  model: string;
  release_year?: number;
  discontinued?: boolean;
  msrp?: number;
  specifications: Record<string, any>;
  variations?: DeviceVariation[];
  created_at?: string;
  updated_at?: string;
}

export interface DeviceVariation {
  id?: number;
  device_id?: number;
  variation_name: string;
  specifications: Record<string, any>;
  price_difference?: number;
  availability?: string;
  created_at?: string;
}

export interface Component {
  id?: number;
  type: ComponentType;
  brand: string;
  model: string;
  series?: string;
  release_year?: number;
  discontinued?: boolean;
  msrp?: number;
  specifications: Record<string, any>;
  performance_metrics?: Record<string, any>;
  compatibility?: string[];
  created_at?: string;
  updated_at?: string;
}

export interface SearchFilter {
  category?: DeviceCategory[];
  component_type?: ComponentType[];
  brand?: string[];
  price_range?: [number, number];
  release_year_range?: [number, number];
  specifications?: Record<string, any>;
  text_search?: string;
}

export interface DatabaseStats {
  total_devices: number;
  total_components: number;
  devices_by_category: Record<DeviceCategory, number>;
  components_by_type: Record<ComponentType, number>;
  recent_additions: number;
  popular_searches: Array<{ query: string; count: number }>;
}

export interface ProductAnalysis {
  productName: string;
  suggestedTitle: string;
  detailedDescription: string;
  keywords: string[];
  pricing: {
    quickSale: {
      price: number;
      justification: string;
    };
    fairMarket: {
      price: number;
      justification: string;
    };
    ambitious: {
      price: number;
      justification: string;
    };
  };
  sources: GroundingSource[];
  searchResults: SearchResult[];
}
