import { describe, it, expect, vi, beforeEach } from 'vitest';
import { getDatabase } from '../../../services/databaseService';

// Mock SQL.js more thoroughly for this test
vi.mock('sql.js', () => ({
  default: vi.fn(() =>
    Promise.resolve({
      Database: vi.fn(() => ({
        exec: vi.fn(() => []),
        close: vi.fn(),
        export: vi.fn(() => new Uint8Array()),
      })),
    })
  ),
}));

describe('DatabaseService', () => {
  let db: any;

  beforeEach(() => {
    vi.clearAllMocks();
    localStorage.clear();
    db = getDatabase();
  });

  it('creates a database instance', () => {
    expect(db).toBeDefined();
  });

  it('normalizes search queries correctly', () => {
    const normalized = db.normalizeSearchQuery('  Apple  ', '  MacBook Pro  ');
    expect(normalized).toBe('apple macbook pro');
  });

  it('handles empty search queries', () => {
    const normalized = db.normalizeSearchQuery('', '');
    expect(normalized).toBe(' ');
  });

  it('normalizes with different cases', () => {
    const normalized = db.normalizeSearchQuery('DELL', 'XPS 13');
    expect(normalized).toBe('dell xps 13');
  });

  it('handles special characters in search queries', () => {
    const normalized = db.normalizeSearchQuery('HP', 'EliteBook 840-G7');
    expect(normalized).toBe('hp elitebook 840-g7');
  });
});
